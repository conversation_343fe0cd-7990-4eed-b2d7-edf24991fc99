using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using MMO.Server.Services;
using MMO.Server.Systems;
using MMO.Server.Managers;
using MMO.Server.Configuration;

namespace MMO.Server
{
    class Program
    {
        static async Task Main(string[] args)
        {
            var host = CreateHostBuilder(args).Build();

            var logger = host.Services.GetRequiredService<ILogger<Program>>();
            logger.LogInformation("Starting Reaper's Passing Game Server...");

            try
            {
                await host.RunAsync();
            }
            catch (Exception ex)
            {
                logger.LogCritical(ex, "Game server crashed");
                throw;
            }
        }

        static IHostBuilder CreateHostBuilder(string[] args) =>
            Host.CreateDefaultBuilder(args)
                .ConfigureWebHostDefaults(webBuilder =>
                {
                    webBuilder.Configure(app =>
                    {
                        // Add health check endpoint
                        app.UseRouting();
                        app.UseEndpoints(endpoints =>
                        {
                            endpoints.MapGet("/health", async context =>
                            {
                                await context.Response.WriteAsync("{\"status\":\"healthy\",\"service\":\"mmo-server\"}");
                            });

                            endpoints.MapPost("/connect", async context =>
                            {
                                // TODO: Implement connection logic
                                await context.Response.WriteAsync("{\"message\":\"Connection endpoint\"}");
                            });

                            endpoints.MapPost("/heartbeat", async context =>
                            {
                                // TODO: Implement heartbeat logic
                                await context.Response.WriteAsync("{\"message\":\"Heartbeat received\"}");
                            });
                        });
                    });

                    // Configure the server to listen on the configured port
                    webBuilder.UseUrls("http://localhost:7778");
                })
                .ConfigureServices((context, services) =>
                {
                    // Configure options
                    services.Configure<DynamoDbConfiguration>(
                        context.Configuration.GetSection(DynamoDbConfiguration.SectionName));
                    services.Configure<GameServerConfiguration>(
                        context.Configuration.GetSection(GameServerConfiguration.SectionName));

                    // DynamoDB Client Factory
                    services.AddSingleton<IDynamoDbClientFactory, DynamoDbClientFactory>();

                    // Core Services
                    services.AddSingleton<IZoneRegistryService, ZoneRegistryService>();
                    services.AddSingleton<IPlayerManager, PlayerManager>();
                    services.AddSingleton<IWorldObjectManager, WorldObjectManager>();

                    // Game Systems
                    services.AddSingleton<ICombatSystem, CombatSystem>();
                    services.AddSingleton<ICyberneticsSystem, CyberneticsSystem>();
                    services.AddSingleton<ICraftingSystem, CraftingSystem>();
                    services.AddSingleton<IEconomySystem, EconomySystem>();
                    services.AddSingleton<ISocialSystem, SocialSystem>();

                    // Persistence
                    services.AddSingleton<IPersistenceService, DynamoDbPersistenceService>();

                    // Main Game Server Service
                    services.AddHostedService<GameServerService>();
                });
    }
}
