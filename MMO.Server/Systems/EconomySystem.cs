using Microsoft.Extensions.Logging;
using MMO.Server.Managers;
using MMO.Server.Services;
using MMO.Shared.Models.Characters;
using MMO.Shared.Models.Common;
using MMO.Shared.Models.Items;
using MMO.Shared.Models.Systems;
using System.Collections.Concurrent;

namespace MMO.Server.Systems
{
    /// <summary>
    /// Handles economy, auction house, and trading
    /// </summary>
    public class EconomySystem : IEconomySystem
    {
        private readonly ILogger<EconomySystem> _logger;
        private readonly IPlayerManager _playerManager;
        private readonly IPersistenceService _persistenceService;
        private readonly ConcurrentDictionary<string, AuctionListing> _activeListings;
        private readonly ConcurrentDictionary<string, TradeOffer> _activeTrades;
        private readonly List<AuctionTransaction> _recentTransactions;
        private readonly object _transactionLock = new object();

        public EconomySystem(
            ILogger<EconomySystem> logger,
            IPlayerManager playerManager,
            IPersistenceService persistenceService)
        {
            _logger = logger;
            _playerManager = playerManager;
            _persistenceService = persistenceService;
            _activeListings = new ConcurrentDictionary<string, AuctionListing>();
            _activeTrades = new ConcurrentDictionary<string, TradeOffer>();
            _recentTransactions = new List<AuctionTransaction>();
        }

        public async Task InitializeAsync()
        {
            // Load active listings from database
            await LoadActiveListingsAsync();
            _logger.LogInformation("Economy system initialized with {Count} active listings", _activeListings.Count);
        }

        public async Task UpdateAsync(float deltaTime)
        {
            // Process expired listings
            await ProcessExpiredListingsAsync();
            
            // Process expired trades
            await ProcessExpiredTradesAsync();
            
            // Process pending deliveries
            await ProcessPendingDeliveriesAsync();
        }

        public async Task<AuctionListingResult> ListItemAsync(string playerId, string itemId, decimal price, int duration, string location)
        {
            try
            {
                var player = _playerManager.GetPlayer(playerId);
                if (player == null)
                {
                    return new AuctionListingResult { Success = false, Message = "Player not found" };
                }

                // Calculate listing fee
                decimal listingFee = price * GameConstants.Economy.AuctionHouseListingFee;
                
                if (player.Credits < listingFee)
                {
                    return new AuctionListingResult { Success = false, Message = "Insufficient credits for listing fee" };
                }

                // TODO: Verify player owns the item and remove it from inventory

                // Create listing
                var listing = new AuctionListing
                {
                    SellerId = playerId,
                    SellerName = player.CharacterName,
                    ItemId = itemId,
                    ItemName = "Sample Item", // TODO: Get from actual item
                    Price = price,
                    Location = location,
                    ExpiresAt = DateTime.UtcNow.AddHours(Math.Min(duration, GameConstants.Economy.MaxAuctionDuration))
                };

                _activeListings.TryAdd(listing.Id, listing);

                // Charge listing fee
                player.Credits -= listingFee;
                _persistenceService.QueueSaveOperation(playerId, player);

                // Save listing to database
                await _persistenceService.SaveWorldObjectAsync($"auction_{listing.Id}", listing);

                _logger.LogInformation("Player {PlayerId} listed item {ItemId} for {Price} credits", 
                    playerId, itemId, price);

                return new AuctionListingResult
                {
                    Success = true,
                    Message = "Item listed successfully",
                    ListingId = listing.Id,
                    ListingFee = listingFee
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error listing item {ItemId} for player {PlayerId}", itemId, playerId);
                return new AuctionListingResult { Success = false, Message = "Listing failed" };
            }
        }

        public async Task<AuctionPurchaseResult> PurchaseItemAsync(string buyerId, string listingId, string deliveryLocation)
        {
            try
            {
                var buyer = _playerManager.GetPlayer(buyerId);
                if (buyer == null)
                {
                    return new AuctionPurchaseResult { Success = false, Message = "Buyer not found" };
                }

                if (!_activeListings.TryGetValue(listingId, out var listing))
                {
                    return new AuctionPurchaseResult { Success = false, Message = "Listing not found" };
                }

                if (listing.Status != AuctionStatus.Active)
                {
                    return new AuctionPurchaseResult { Success = false, Message = "Listing is no longer active" };
                }

                if (listing.HasExpired)
                {
                    listing.Status = AuctionStatus.Expired;
                    return new AuctionPurchaseResult { Success = false, Message = "Listing has expired" };
                }

                // Calculate costs
                decimal salesTax = listing.Price * GameConstants.Economy.AuctionHouseSalesTax;
                decimal shippingCost = CalculateShippingCost(listing.Location, deliveryLocation);
                decimal totalCost = listing.Price + salesTax + shippingCost;

                if (buyer.Credits < totalCost)
                {
                    return new AuctionPurchaseResult { Success = false, Message = "Insufficient credits" };
                }

                // Process transaction
                var transaction = new AuctionTransaction
                {
                    ListingId = listingId,
                    SellerId = listing.SellerId,
                    BuyerId = buyerId,
                    ItemId = listing.ItemId,
                    ItemName = listing.ItemName,
                    Price = listing.Price,
                    SalesTax = salesTax,
                    ShippingCost = shippingCost,
                    TotalCost = totalCost,
                    PickupLocation = listing.Location,
                    DeliveryLocation = deliveryLocation,
                    EstimatedDelivery = CalculateDeliveryTime(listing.Location, deliveryLocation)
                };

                // Charge buyer
                buyer.Credits -= totalCost;
                _persistenceService.QueueSaveOperation(buyerId, buyer);

                // Pay seller (minus sales tax)
                var seller = _playerManager.GetPlayer(listing.SellerId);
                if (seller != null)
                {
                    seller.Credits += listing.Price; // Seller gets full price, tax goes to "house"
                    _persistenceService.QueueSaveOperation(listing.SellerId, seller);
                }

                // Mark listing as sold
                listing.Status = AuctionStatus.Sold;
                _activeListings.TryRemove(listingId, out _);

                // Record transaction
                lock (_transactionLock)
                {
                    _recentTransactions.Add(transaction);
                }

                await _persistenceService.SaveWorldObjectAsync($"transaction_{transaction.Id}", transaction);

                _logger.LogInformation("Player {BuyerId} purchased item {ItemId} from {SellerId} for {Price} credits", 
                    buyerId, listing.ItemId, listing.SellerId, listing.Price);

                return new AuctionPurchaseResult
                {
                    Success = true,
                    Message = "Purchase successful",
                    TotalCost = totalCost,
                    SalesTax = salesTax,
                    ShippingCost = shippingCost,
                    EstimatedDelivery = transaction.EstimatedDelivery
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error purchasing item {ListingId} for player {BuyerId}", listingId, buyerId);
                return new AuctionPurchaseResult { Success = false, Message = "Purchase failed" };
            }
        }

        public async Task<bool> CancelListingAsync(string playerId, string listingId)
        {
            try
            {
                if (!_activeListings.TryGetValue(listingId, out var listing))
                    return false;

                if (listing.SellerId != playerId)
                    return false;

                if (listing.Status != AuctionStatus.Active)
                    return false;

                // Cancel listing
                listing.Status = AuctionStatus.Cancelled;
                _activeListings.TryRemove(listingId, out _);

                // TODO: Return item to player's inventory

                _logger.LogInformation("Player {PlayerId} cancelled listing {ListingId}", playerId, listingId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cancelling listing {ListingId} for player {PlayerId}", listingId, playerId);
                return false;
            }
        }

        public async Task<List<AuctionListing>> SearchListingsAsync(AuctionSearchCriteria criteria)
        {
            try
            {
                var results = _activeListings.Values
                    .Where(l => l.Status == AuctionStatus.Active && !l.HasExpired)
                    .AsEnumerable();

                // Apply filters
                if (!string.IsNullOrEmpty(criteria.ItemName))
                {
                    results = results.Where(l => l.ItemName.Contains(criteria.ItemName, StringComparison.OrdinalIgnoreCase));
                }

                if (criteria.MinPrice.HasValue)
                {
                    results = results.Where(l => l.Price >= criteria.MinPrice.Value);
                }

                if (criteria.MaxPrice.HasValue)
                {
                    results = results.Where(l => l.Price <= criteria.MaxPrice.Value);
                }

                if (!string.IsNullOrEmpty(criteria.Location))
                {
                    results = results.Where(l => l.Location.Equals(criteria.Location, StringComparison.OrdinalIgnoreCase));
                }

                // Apply sorting
                results = criteria.SortBy switch
                {
                    AuctionSortBy.PriceAscending => results.OrderBy(l => l.Price),
                    AuctionSortBy.PriceDescending => results.OrderByDescending(l => l.Price),
                    AuctionSortBy.TimeRemaining => results.OrderBy(l => l.TimeRemaining),
                    AuctionSortBy.RecentlyListed => results.OrderByDescending(l => l.ListedAt),
                    AuctionSortBy.ItemName => results.OrderBy(l => l.ItemName),
                    AuctionSortBy.Location => results.OrderBy(l => l.Location),
                    _ => results.OrderBy(l => l.Price)
                };

                return results.Skip(criteria.Skip).Take(criteria.MaxResults).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching auction listings");
                return new List<AuctionListing>();
            }
        }

        public async Task<TradeResult> ProcessTradeAsync(string player1Id, string player2Id, TradeOffer offer)
        {
            try
            {
                var player1 = _playerManager.GetPlayer(player1Id);
                var player2 = _playerManager.GetPlayer(player2Id);

                if (player1 == null || player2 == null)
                {
                    return new TradeResult { Success = false, Message = "One or both players not found" };
                }

                if (!offer.IsBothAccepted)
                {
                    return new TradeResult { Success = false, Message = "Both players must accept the trade" };
                }

                if (offer.HasExpired)
                {
                    return new TradeResult { Success = false, Message = "Trade offer has expired" };
                }

                // Verify players have sufficient credits
                if (player1.Credits < offer.Player1Credits || player2.Credits < offer.Player2Credits)
                {
                    return new TradeResult { Success = false, Message = "Insufficient credits" };
                }

                // TODO: Verify players have the offered items

                // Execute trade
                player1.Credits -= offer.Player1Credits;
                player1.Credits += offer.Player2Credits;
                player2.Credits -= offer.Player2Credits;
                player2.Credits += offer.Player1Credits;

                // TODO: Transfer items between players

                // Save changes
                _persistenceService.QueueSaveOperation(player1Id, player1);
                _persistenceService.QueueSaveOperation(player2Id, player2);

                offer.Status = TradeStatus.Completed;
                _activeTrades.TryRemove(offer.Id, out _);

                _logger.LogInformation("Trade completed between {Player1Id} and {Player2Id}", player1Id, player2Id);

                return new TradeResult
                {
                    Success = true,
                    Message = "Trade completed successfully",
                    TradeId = offer.Id
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing trade between {Player1Id} and {Player2Id}", player1Id, player2Id);
                return new TradeResult { Success = false, Message = "Trade failed" };
            }
        }

        public async Task<List<AuctionListing>> GetPlayerListingsAsync(string playerId)
        {
            return _activeListings.Values
                .Where(l => l.SellerId == playerId)
                .OrderByDescending(l => l.ListedAt)
                .ToList();
        }

        public async Task<List<AuctionTransaction>> GetPlayerTransactionsAsync(string playerId)
        {
            lock (_transactionLock)
            {
                return _recentTransactions
                    .Where(t => t.SellerId == playerId || t.BuyerId == playerId)
                    .OrderByDescending(t => t.TransactionDate)
                    .Take(50)
                    .ToList();
            }
        }

        private async Task LoadActiveListingsAsync()
        {
            // TODO: Load from database
            // For now, start with empty listings
        }

        private async Task ProcessExpiredListingsAsync()
        {
            var expiredListings = _activeListings.Values
                .Where(l => l.HasExpired && l.Status == AuctionStatus.Active)
                .ToList();

            foreach (var listing in expiredListings)
            {
                listing.Status = AuctionStatus.Expired;
                _activeListings.TryRemove(listing.Id, out _);

                // TODO: Return item to seller's inventory

                _logger.LogDebug("Listing {ListingId} expired", listing.Id);
            }
        }

        private async Task ProcessExpiredTradesAsync()
        {
            var expiredTrades = _activeTrades.Values
                .Where(t => t.HasExpired && t.Status == TradeStatus.Pending)
                .ToList();

            foreach (var trade in expiredTrades)
            {
                trade.Status = TradeStatus.Expired;
                _activeTrades.TryRemove(trade.Id, out _);

                _logger.LogDebug("Trade {TradeId} expired", trade.Id);
            }
        }

        private async Task ProcessPendingDeliveriesAsync()
        {
            // TODO: Process items that should be delivered to players
        }

        private decimal CalculateShippingCost(string fromLocation, string toLocation)
        {
            if (fromLocation.Equals(toLocation, StringComparison.OrdinalIgnoreCase))
                return 0; // No shipping cost for same location

            // Simple shipping cost calculation
            // In a real implementation, this would be based on actual distances
            return 50m; // Base shipping cost
        }

        private DateTime CalculateDeliveryTime(string fromLocation, string toLocation)
        {
            if (fromLocation.Equals(toLocation, StringComparison.OrdinalIgnoreCase))
                return DateTime.UtcNow.AddHours(GameConstants.Economy.LocalShippingTime);

            // Simple delivery time calculation
            return DateTime.UtcNow.AddHours(GameConstants.Economy.RegionalShippingTime);
        }
    }
}
