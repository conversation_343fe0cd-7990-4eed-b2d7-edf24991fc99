using Microsoft.Extensions.Logging;
using MMO.Server.Managers;
using MMO.Shared.Models.Characters;
using MMO.Shared.Models.Common;
using MMO.Shared.Models.Items;
using System.Collections.Concurrent;

namespace MMO.Server.Systems
{
    /// <summary>
    /// Handles all combat-related logic
    /// </summary>
    public class CombatSystem : ICombatSystem
    {
        private readonly ILogger<CombatSystem> _logger;
        private readonly IPlayerManager _playerManager;
        private readonly ConcurrentDictionary<string, DateTime> _combatStates;
        private readonly Random _random;

        public CombatSystem(ILogger<CombatSystem> logger, IPlayerManager playerManager)
        {
            _logger = logger;
            _playerManager = playerManager;
            _combatStates = new ConcurrentDictionary<string, DateTime>();
            _random = new Random();
        }

        public async Task InitializeAsync()
        {
            _logger.LogInformation("Combat system initialized");
        }

        public async Task UpdateAsync(float deltaTime)
        {
            // Check for players who should exit combat
            var now = DateTime.UtcNow;
            var playersToRemove = new List<string>();

            foreach (var kvp in _combatStates)
            {
                if (now - kvp.Value > TimeSpan.FromSeconds(10)) // 10 seconds out of combat
                {
                    playersToRemove.Add(kvp.Key);
                }
            }

            foreach (var playerId in playersToRemove)
            {
                await EndCombatAsync(playerId);
            }
        }

        public async Task<CombatResult> ProcessWeaponFireAsync(string attackerId, string weaponId, Vector3 targetPosition, string? targetId = null)
        {
            try
            {
                var attacker = _playerManager.GetPlayer(attackerId);
                if (attacker == null)
                {
                    return new CombatResult { Success = false, Message = "Attacker not found" };
                }

                // TODO: Get weapon from attacker's inventory/equipment
                // For now, simulate a basic weapon
                var weapon = new Weapon
                {
                    BaseDamage = 25f,
                    BaseAccuracy = 0.8f,
                    PrimaryDamageType = DamageType.Ballistic,
                    EffectiveRange = 50f
                };

                // Calculate range to target
                float range = Vector3.Distance(attacker.Position, targetPosition);

                // Calculate accuracy
                float accuracy = weapon.CalculateAccuracyAtRange(range);

                // Roll for hit
                bool hit = _random.NextSingle() <= accuracy;
                if (!hit)
                {
                    return new CombatResult 
                    { 
                        Success = true, 
                        Hit = false, 
                        Message = "Shot missed" 
                    };
                }

                // Determine hit location
                var hitLocation = DetermineHitLocation();
                
                // Calculate damage
                float baseDamage = weapon.CalculateDamageAtRange(range);
                
                // Check for critical hit
                bool isCritical = _random.NextSingle() <= weapon.CriticalChance;
                if (isCritical)
                {
                    baseDamage *= weapon.CriticalDamageMultiplier;
                }

                // Check for headshot
                bool isHeadshot = hitLocation == HitLocation.Head;
                if (isHeadshot)
                {
                    baseDamage *= GameConstants.Combat.HeadshotMultiplier;
                }

                // Apply hit location modifier
                baseDamage *= GetHitLocationMultiplier(hitLocation);

                // Start combat for attacker
                await StartCombatAsync(attackerId);

                var result = new CombatResult
                {
                    Success = true,
                    Hit = true,
                    Damage = baseDamage,
                    DamageType = weapon.PrimaryDamageType,
                    HitLocation = hitLocation,
                    IsCritical = isCritical,
                    IsHeadshot = isHeadshot
                };

                // Apply damage to target if specified
                if (!string.IsNullOrEmpty(targetId))
                {
                    var target = _playerManager.GetPlayer(targetId);
                    if (target != null)
                    {
                        // Calculate final damage with armor
                        float armorRating = target.GetTotalArmorRating();
                        float finalDamage = CalculateDamage(baseDamage, weapon.PrimaryDamageType, hitLocation, armorRating);
                        
                        result.Damage = finalDamage;
                        result.TargetKilled = await ApplyDamageAsync(targetId, finalDamage, weapon.PrimaryDamageType, attackerId);
                        
                        // Start combat for target
                        await StartCombatAsync(targetId);
                    }
                }

                _logger.LogDebug("Combat: {AttackerId} fired weapon, hit: {Hit}, damage: {Damage}, target: {TargetId}", 
                    attackerId, hit, result.Damage, targetId);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing weapon fire from {AttackerId}", attackerId);
                return new CombatResult { Success = false, Message = "Internal error" };
            }
        }

        public float CalculateDamage(float baseDamage, DamageType damageType, HitLocation hitLocation, float armorRating)
        {
            // Apply damage type modifiers based on armor type
            float damageMultiplier = damageType switch
            {
                DamageType.Ballistic => GameConstants.Combat.DamageModifiers.BallisticVsArmor,
                DamageType.Energy => GameConstants.Combat.DamageModifiers.EnergyVsArmor,
                DamageType.EMP => GameConstants.Combat.DamageModifiers.EMPVsArmor,
                _ => 1f
            };

            // Calculate armor reduction
            float armorReduction = armorRating / (armorRating + 100f); // Diminishing returns
            
            // Apply damage
            float finalDamage = baseDamage * damageMultiplier * (1f - armorReduction);
            
            return Math.Max(1f, finalDamage); // Minimum 1 damage
        }

        public async Task<bool> ApplyDamageAsync(string targetId, float damage, DamageType damageType, string sourceId)
        {
            return await _playerManager.DamagePlayerAsync(targetId, damage, sourceId);
        }

        public bool IsPlayerInCombat(string playerId)
        {
            return _combatStates.ContainsKey(playerId);
        }

        public async Task StartCombatAsync(string playerId)
        {
            _combatStates.AddOrUpdate(playerId, DateTime.UtcNow, (key, oldValue) => DateTime.UtcNow);
            
            var player = _playerManager.GetPlayer(playerId);
            if (player != null)
            {
                // Add in-combat status effect if not already present
                if (!player.ActiveStatusEffects.Any(e => e.Type == StatusEffectType.InCombat))
                {
                    var combatEffect = new StatusEffect
                    {
                        Type = StatusEffectType.InCombat,
                        Name = "In Combat",
                        Description = "Currently engaged in combat",
                        IsPermanent = true // Will be removed when combat ends
                    };
                    
                    player.ActiveStatusEffects.Add(combatEffect);
                    _logger.LogDebug("Player {PlayerId} entered combat", playerId);
                }
            }
        }

        public async Task EndCombatAsync(string playerId)
        {
            if (_combatStates.TryRemove(playerId, out _))
            {
                var player = _playerManager.GetPlayer(playerId);
                if (player != null)
                {
                    // Remove in-combat status effect
                    player.ActiveStatusEffects.RemoveAll(e => e.Type == StatusEffectType.InCombat);
                    _logger.LogDebug("Player {PlayerId} exited combat", playerId);
                }
            }
        }

        private HitLocation DetermineHitLocation()
        {
            var roll = _random.NextSingle();
            
            return roll switch
            {
                < 0.1f => HitLocation.Head,      // 10% chance
                < 0.6f => HitLocation.Torso,    // 50% chance
                < 0.75f => HitLocation.LeftArm, // 15% chance
                < 0.9f => HitLocation.RightArm, // 15% chance
                < 0.95f => HitLocation.LeftLeg, // 5% chance
                _ => HitLocation.RightLeg        // 5% chance
            };
        }

        private float GetHitLocationMultiplier(HitLocation hitLocation)
        {
            return hitLocation switch
            {
                HitLocation.Head => GameConstants.Combat.HitLocationMultipliers.Head,
                HitLocation.Torso => GameConstants.Combat.HitLocationMultipliers.Torso,
                HitLocation.LeftArm or HitLocation.RightArm => GameConstants.Combat.HitLocationMultipliers.Arms,
                HitLocation.LeftLeg or HitLocation.RightLeg => GameConstants.Combat.HitLocationMultipliers.Legs,
                _ => 1f
            };
        }
    }
}
