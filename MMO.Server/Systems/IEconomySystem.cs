using MMO.Shared.Models.Characters;
using MMO.Shared.Models.Items;
using MMO.Shared.Models.Systems;

namespace MMO.Server.Systems
{
    /// <summary>
    /// Handles economy, auction house, and trading
    /// </summary>
    public interface IEconomySystem
    {
        Task InitializeAsync();
        Task UpdateAsync(float deltaTime);

        /// <summary>
        /// Lists an item on the auction house
        /// </summary>
        Task<AuctionListingResult> ListItemAsync(string playerId, string itemId, decimal price, int duration, string location);

        /// <summary>
        /// Purchases an item from the auction house
        /// </summary>
        Task<AuctionPurchaseResult> PurchaseItemAsync(string buyerId, string listingId, string deliveryLocation);

        /// <summary>
        /// Cancels an auction listing
        /// </summary>
        Task<bool> CancelListingAsync(string playerId, string listingId);

        /// <summary>
        /// Searches auction house listings
        /// </summary>
        Task<List<AuctionListing>> SearchListingsAsync(AuctionSearchCriteria criteria);

        /// <summary>
        /// Processes a direct trade between players
        /// </summary>
        Task<TradeResult> ProcessTradeAsync(string player1Id, string player2Id, TradeOffer offer);

        /// <summary>
        /// Gets player's active listings
        /// </summary>
        Task<List<AuctionListing>> GetPlayerListingsAsync(string playerId);

        /// <summary>
        /// Gets player's purchase history
        /// </summary>
        Task<List<AuctionTransaction>> GetPlayerTransactionsAsync(string playerId);
    }

    public class AuctionListingResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public string ListingId { get; set; } = string.Empty;
        public decimal ListingFee { get; set; }
    }

    public class AuctionPurchaseResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public decimal TotalCost { get; set; }
        public decimal SalesTax { get; set; }
        public decimal ShippingCost { get; set; }
        public DateTime EstimatedDelivery { get; set; }
    }

    public class TradeResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public string TradeId { get; set; } = string.Empty;
    }
}
