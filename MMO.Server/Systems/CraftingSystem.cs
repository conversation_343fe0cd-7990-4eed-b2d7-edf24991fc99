using Microsoft.Extensions.Logging;
using MMO.Server.Managers;
using MMO.Server.Services;
using MMO.Shared.Models.Characters;
using MMO.Shared.Models.Common;
using MMO.Shared.Models.Items;
using MMO.Shared.Models.Systems;
using System.Collections.Concurrent;

namespace MMO.Server.Systems
{
    /// <summary>
    /// Handles item crafting and modification
    /// </summary>
    public class CraftingSystem : ICraftingSystem
    {
        private readonly ILogger<CraftingSystem> _logger;
        private readonly IPlayerManager _playerManager;
        private readonly IPersistenceService _persistenceService;
        private readonly ConcurrentDictionary<string, CraftingOperation> _activeCrafting;
        private readonly Dictionary<string, CraftingSchematic> _schematics;
        private readonly Random _random;

        public CraftingSystem(
            ILogger<CraftingSystem> logger,
            IPlayerManager playerManager,
            IPersistenceService persistenceService)
        {
            _logger = logger;
            _playerManager = playerManager;
            _persistenceService = persistenceService;
            _activeCrafting = new ConcurrentDictionary<string, CraftingOperation>();
            _schematics = new Dictionary<string, CraftingSchematic>();
            _random = new Random();
        }

        public async Task InitializeAsync()
        {
            // Load crafting schematics
            await LoadSchematicsAsync();
            _logger.LogInformation("Crafting system initialized with {Count} schematics", _schematics.Count);
        }

        public async Task UpdateAsync(float deltaTime)
        {
            var completedOperations = new List<string>();

            foreach (var kvp in _activeCrafting)
            {
                var operation = kvp.Value;
                operation.TimeRemaining -= deltaTime;

                if (operation.TimeRemaining <= 0)
                {
                    await CompleteCraftingOperationAsync(operation);
                    completedOperations.Add(kvp.Key);
                }
            }

            // Remove completed operations
            foreach (var operationId in completedOperations)
            {
                _activeCrafting.TryRemove(operationId, out _);
            }
        }

        public async Task<CraftingResult> StartCraftingAsync(string playerId, string schematicId, Dictionary<string, int> components)
        {
            try
            {
                var player = _playerManager.GetPlayer(playerId);
                if (player == null)
                {
                    return new CraftingResult { Success = false, Message = "Player not found" };
                }

                if (!_schematics.TryGetValue(schematicId, out var schematic))
                {
                    return new CraftingResult { Success = false, Message = "Schematic not found" };
                }

                // Check if player knows this schematic
                if (!player.KnownSchematicIds.Contains(schematicId))
                {
                    return new CraftingResult { Success = false, Message = "Unknown schematic" };
                }

                // Check skill requirements
                if (!CanCraftSchematic(player, schematic))
                {
                    return new CraftingResult { Success = false, Message = "Insufficient skills" };
                }

                // Check component requirements
                if (!HasRequiredComponents(components, schematic.RequiredComponents))
                {
                    return new CraftingResult { Success = false, Message = "Missing required components" };
                }

                // Calculate success chance
                float successChance = CalculateSuccessChance(player, schematic);
                
                // Calculate crafting time (modified by skill)
                float craftingTime = CalculateCraftingTime(player, schematic);

                var operationId = Guid.NewGuid().ToString();
                var operation = new CraftingOperation
                {
                    Id = operationId,
                    PlayerId = playerId,
                    SchematicId = schematicId,
                    Components = components,
                    TimeRemaining = craftingTime,
                    TotalTime = craftingTime,
                    SuccessChance = successChance
                };

                _activeCrafting.TryAdd(operationId, operation);

                _logger.LogInformation("Player {PlayerId} started crafting {SchematicId} with {SuccessChance:P} success chance", 
                    playerId, schematicId, successChance);

                return new CraftingResult
                {
                    Success = true,
                    Message = "Crafting started",
                    OperationId = operationId,
                    CraftingTime = craftingTime
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error starting crafting for player {PlayerId}", playerId);
                return new CraftingResult { Success = false, Message = "Crafting failed to start" };
            }
        }

        public async Task<bool> CancelCraftingAsync(string playerId, string operationId)
        {
            try
            {
                if (_activeCrafting.TryGetValue(operationId, out var operation))
                {
                    if (operation.PlayerId == playerId)
                    {
                        _activeCrafting.TryRemove(operationId, out _);
                        
                        // Return 50% of components
                        await ReturnComponents(playerId, operation.Components, 0.5f);
                        
                        _logger.LogInformation("Player {PlayerId} cancelled crafting operation {OperationId}", 
                            playerId, operationId);
                        return true;
                    }
                }
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cancelling crafting for player {PlayerId}", playerId);
                return false;
            }
        }

        public async Task<ModificationResult> ModifyItemAsync(string playerId, string itemId, ItemMod modification)
        {
            try
            {
                var player = _playerManager.GetPlayer(playerId);
                if (player == null)
                {
                    return new ModificationResult { Success = false, Message = "Player not found" };
                }

                // TODO: Get item from player's inventory
                // For now, simulate item modification
                
                // Check skill requirements
                if (!modification.CanBeInstalledBy(player.Skills, player.Level))
                {
                    return new ModificationResult { Success = false, Message = "Insufficient skills" };
                }

                // Calculate success chance based on player skills
                float successChance = CalculateModificationSuccessChance(player, modification);
                bool success = _random.NextSingle() <= successChance;

                if (success)
                {
                    // Apply modification
                    float qualityBonus = CalculateQualityBonus(player, modification);
                    int experience = CalculateModificationExperience(modification);

                    // Add experience to relevant skill
                    await _playerManager.AddExperienceAsync(playerId, experience);

                    _logger.LogInformation("Player {PlayerId} successfully modified item {ItemId}", 
                        playerId, itemId);

                    return new ModificationResult
                    {
                        Success = true,
                        Message = "Modification successful",
                        QualityBonus = qualityBonus,
                        ExperienceGained = experience
                    };
                }
                else
                {
                    return new ModificationResult 
                    { 
                        Success = false, 
                        Message = "Modification failed" 
                    };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error modifying item {ItemId} for player {PlayerId}", itemId, playerId);
                return new ModificationResult { Success = false, Message = "Modification error" };
            }
        }

        public async Task<bool> RemoveModificationAsync(string playerId, string itemId, string modId)
        {
            try
            {
                var player = _playerManager.GetPlayer(playerId);
                if (player == null) return false;

                // TODO: Implement modification removal
                // This would involve finding the item and removing the specific mod

                _logger.LogInformation("Player {PlayerId} removed modification {ModId} from item {ItemId}", 
                    playerId, modId, itemId);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing modification {ModId} from item {ItemId} for player {PlayerId}", 
                    modId, itemId, playerId);
                return false;
            }
        }

        public async Task<RepairResult> RepairItemAsync(string playerId, string itemId, Dictionary<string, int> materials)
        {
            try
            {
                var player = _playerManager.GetPlayer(playerId);
                if (player == null)
                {
                    return new RepairResult { Success = false, Message = "Player not found" };
                }

                // TODO: Get item from player's inventory
                // For now, simulate repair

                // Check if player has repair skills
                int repairSkill = player.Skills.GetValueOrDefault("Repair", 0);
                if (repairSkill < 10)
                {
                    return new RepairResult { Success = false, Message = "Insufficient repair skill" };
                }

                // Calculate repair efficiency based on skill
                float efficiency = GameConstants.Crafting.RepairEfficiency + (repairSkill * 0.01f);
                efficiency = Math.Min(1f, efficiency); // Cap at 100%

                // Calculate durability restored
                float durabilityRestored = 50f * efficiency; // Base 50 durability

                // Calculate experience gained
                int experience = (int)(durabilityRestored * 2);
                await _playerManager.AddExperienceAsync(playerId, experience);

                _logger.LogInformation("Player {PlayerId} repaired item {ItemId} for {Durability} durability", 
                    playerId, itemId, durabilityRestored);

                return new RepairResult
                {
                    Success = true,
                    Message = "Item repaired successfully",
                    DurabilityRestored = durabilityRestored,
                    RepairEfficiency = efficiency,
                    ExperienceGained = experience
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error repairing item {ItemId} for player {PlayerId}", itemId, playerId);
                return new RepairResult { Success = false, Message = "Repair failed" };
            }
        }

        public async Task<List<CraftingSchematic>> GetAvailableSchematicsAsync(string playerId)
        {
            try
            {
                var player = _playerManager.GetPlayer(playerId);
                if (player == null) return new List<CraftingSchematic>();

                var availableSchematics = new List<CraftingSchematic>();

                foreach (var schematicId in player.KnownSchematicIds)
                {
                    if (_schematics.TryGetValue(schematicId, out var schematic))
                    {
                        if (CanCraftSchematic(player, schematic))
                        {
                            availableSchematics.Add(schematic);
                        }
                    }
                }

                return availableSchematics;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting available schematics for player {PlayerId}", playerId);
                return new List<CraftingSchematic>();
            }
        }

        private async Task LoadSchematicsAsync()
        {
            // TODO: Load from database or configuration
            // For now, create some sample schematics
            
            var basicWeaponSchematic = new CraftingSchematic
            {
                Id = "basic_pistol",
                Name = "Basic Pistol",
                Description = "A simple ballistic pistol",
                Category = "Weapons",
                SkillRequirements = new Dictionary<string, int> { { "Firearms", 25 }, { "Crafting", 20 } },
                RequiredComponents = new Dictionary<string, int> 
                { 
                    { "steel", 5 }, 
                    { "polymer", 3 }, 
                    { "spring", 2 } 
                },
                OutputItemId = "basic_pistol_item",
                OutputQuantity = 1,
                SuccessChance = 0.8f,
                CraftingTime = 300f // 5 minutes
            };

            _schematics.Add(basicWeaponSchematic.Id, basicWeaponSchematic);
        }

        private async Task CompleteCraftingOperationAsync(CraftingOperation operation)
        {
            try
            {
                var player = _playerManager.GetPlayer(operation.PlayerId);
                if (player == null) return;

                bool success = _random.NextSingle() <= operation.SuccessChance;

                if (success)
                {
                    // Create the crafted item
                    // TODO: Actually create and add item to player inventory
                    
                    // Award experience
                    int experience = 100; // Base experience
                    await _playerManager.AddExperienceAsync(operation.PlayerId, experience);

                    _logger.LogInformation("Player {PlayerId} successfully completed crafting {SchematicId}", 
                        operation.PlayerId, operation.SchematicId);
                }
                else
                {
                    // Crafting failed, return some components
                    await ReturnComponents(operation.PlayerId, operation.Components, 0.25f);

                    _logger.LogInformation("Player {PlayerId} failed crafting {SchematicId}", 
                        operation.PlayerId, operation.SchematicId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error completing crafting operation {OperationId}", operation.Id);
            }
        }

        private bool CanCraftSchematic(Player player, CraftingSchematic schematic)
        {
            // Check level requirement
            if (player.Level < schematic.LevelRequirement)
                return false;

            // Check skill requirements
            foreach (var requirement in schematic.SkillRequirements)
            {
                if (!player.HasSkill(requirement.Key, requirement.Value))
                    return false;
            }

            return true;
        }

        private bool HasRequiredComponents(Dictionary<string, int> available, Dictionary<string, int> required)
        {
            foreach (var requirement in required)
            {
                if (!available.ContainsKey(requirement.Key) || 
                    available[requirement.Key] < requirement.Value)
                {
                    return false;
                }
            }
            return true;
        }

        private float CalculateSuccessChance(Player player, CraftingSchematic schematic)
        {
            float baseChance = schematic.SuccessChance;
            
            // Bonus from relevant skills
            foreach (var skill in schematic.SkillRequirements)
            {
                int playerSkill = player.Skills.GetValueOrDefault(skill.Key, 0);
                float bonus = (playerSkill - skill.Value) * GameConstants.Crafting.SkillBonusPerLevel;
                baseChance += bonus;
            }

            return Math.Max(0.1f, Math.Min(0.95f, baseChance)); // Clamp between 10% and 95%
        }

        private float CalculateCraftingTime(Player player, CraftingSchematic schematic)
        {
            float baseTime = schematic.CraftingTime;
            
            // Reduce time based on skill level
            int craftingSkill = player.Skills.GetValueOrDefault("Crafting", 0);
            float timeReduction = craftingSkill * 0.01f; // 1% per skill level
            
            return baseTime * (1f - Math.Min(0.5f, timeReduction)); // Max 50% reduction
        }

        private float CalculateModificationSuccessChance(Player player, ItemMod modification)
        {
            int relevantSkill = player.Skills.GetValueOrDefault("Crafting", 0);
            return GameConstants.Crafting.SkillSuccessBaseChance + (relevantSkill * GameConstants.Crafting.SkillBonusPerLevel);
        }

        private float CalculateQualityBonus(Player player, ItemMod modification)
        {
            int craftingSkill = player.Skills.GetValueOrDefault("Crafting", 0);
            return craftingSkill * 0.005f; // 0.5% bonus per skill level
        }

        private int CalculateModificationExperience(ItemMod modification)
        {
            return (int)(modification.ValueModifier * 0.1m); // Experience based on mod value
        }

        private async Task ReturnComponents(string playerId, Dictionary<string, int> components, float percentage)
        {
            // TODO: Actually return components to player inventory
            _logger.LogDebug("Returning {Percentage:P} of components to player {PlayerId}", percentage, playerId);
        }
    }

    public class CraftingOperation
    {
        public string Id { get; set; } = string.Empty;
        public string PlayerId { get; set; } = string.Empty;
        public string SchematicId { get; set; } = string.Empty;
        public Dictionary<string, int> Components { get; set; } = new Dictionary<string, int>();
        public float TimeRemaining { get; set; }
        public float TotalTime { get; set; }
        public float SuccessChance { get; set; }
    }
}
