using MMO.Shared.Models.Characters;
using MMO.Shared.Models.Systems;

namespace MMO.Server.Systems
{
    /// <summary>
    /// Handles guilds, housing, and social features
    /// </summary>
    public interface ISocialSystem
    {
        Task InitializeAsync();
        Task UpdateAsync(float deltaTime);

        // Guild Management
        Task<GuildCreationResult> CreateGuildAsync(string founderId, string guildName, string description);
        Task<bool> DisbandGuildAsync(string guildId, string leaderId);
        Task<bool> InvitePlayerToGuildAsync(string guildId, string inviterId, string inviteeId);
        Task<bool> AcceptGuildInviteAsync(string playerId, string guildId);
        Task<bool> LeaveGuildAsync(string playerId);
        Task<bool> KickPlayerFromGuildAsync(string guildId, string kickerId, string targetId);
        Task<bool> PromotePlayerAsync(string guildId, string promoterId, string targetId);
        Task<bool> DemotePlayerAsync(string guildId, string demoterId, string targetId);

        // Housing Management
        Task<HousingResult> PurchaseHouseAsync(string playerId, string houseType, string location);
        Task<bool> SellHouseAsync(string playerId, string houseId);
        Task<bool> UpgradeHouseAsync(string playerId, string houseId, string upgradeType);
        Task<bool> SetHousePermissionsAsync(string playerId, string houseId, HousePermissions permissions);
        Task<List<PlayerHouse>> GetPlayerHousesAsync(string playerId);

        // Social Features
        Task<bool> SendFriendRequestAsync(string senderId, string receiverId);
        Task<bool> AcceptFriendRequestAsync(string playerId, string friendId);
        Task<bool> RemoveFriendAsync(string playerId, string friendId);
        Task<List<string>> GetPlayerFriendsAsync(string playerId);
    }

    public class GuildCreationResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public string GuildId { get; set; } = string.Empty;
        public decimal CreationCost { get; set; }
    }

    public class HousingResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public string HouseId { get; set; } = string.Empty;
        public decimal Cost { get; set; }
    }
}
