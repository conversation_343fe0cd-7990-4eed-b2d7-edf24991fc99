using MMO.Shared.Models.Characters;
using MMO.Shared.Models.Items;
using MMO.Shared.Models.Systems;

namespace MMO.Server.Systems
{
    /// <summary>
    /// Handles item crafting and modification
    /// </summary>
    public interface ICraftingSystem
    {
        Task InitializeAsync();
        Task UpdateAsync(float deltaTime);

        /// <summary>
        /// Starts a crafting operation
        /// </summary>
        Task<CraftingResult> StartCraftingAsync(string playerId, string schematicId, Dictionary<string, int> components);

        /// <summary>
        /// Cancels an active crafting operation
        /// </summary>
        Task<bool> CancelCraftingAsync(string playerId, string operationId);

        /// <summary>
        /// Adds a modification to an item
        /// </summary>
        Task<ModificationResult> ModifyItemAsync(string playerId, string itemId, ItemMod modification);

        /// <summary>
        /// Removes a modification from an item
        /// </summary>
        Task<bool> RemoveModificationAsync(string playerId, string itemId, string modId);

        /// <summary>
        /// Repairs an item
        /// </summary>
        Task<RepairResult> RepairItemAsync(string playerId, string itemId, Dictionary<string, int> materials);

        /// <summary>
        /// Gets available schematics for a player
        /// </summary>
        Task<List<CraftingSchematic>> GetAvailableSchematicsAsync(string playerId);
    }

    public class CraftingResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public string OperationId { get; set; } = string.Empty;
        public Item? CraftedItem { get; set; }
        public float CraftingTime { get; set; }
        public int ExperienceGained { get; set; }
    }

    public class ModificationResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public float QualityBonus { get; set; }
        public int ExperienceGained { get; set; }
    }

    public class RepairResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public float DurabilityRestored { get; set; }
        public float RepairEfficiency { get; set; }
        public int ExperienceGained { get; set; }
    }
}
