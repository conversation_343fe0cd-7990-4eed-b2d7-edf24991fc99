using Microsoft.Extensions.Logging;
using MMO.Server.Managers;
using MMO.Server.Services;
using MMO.Shared.Models.Characters;
using MMO.Shared.Models.Common;
using MMO.Shared.Models.Items;
using MMO.Shared.Models.Systems;
using System.Collections.Concurrent;

namespace MMO.Server.Systems
{
    /// <summary>
    /// Handles cybernetic implant systems and abilities
    /// </summary>
    public class CyberneticsSystem : ICyberneticsSystem
    {
        private readonly ILogger<CyberneticsSystem> _logger;
        private readonly IPlayerManager _playerManager;
        private readonly IPersistenceService _persistenceService;
        private readonly ConcurrentDictionary<string, List<CyberneticImplant>> _playerImplants;
        private readonly Random _random;

        public CyberneticsSystem(
            ILogger<CyberneticsSystem> logger,
            IPlayerManager playerManager,
            IPersistenceService persistenceService)
        {
            _logger = logger;
            _playerManager = playerManager;
            _persistenceService = persistenceService;
            _playerImplants = new ConcurrentDictionary<string, List<CyberneticImplant>>();
            _random = new Random();
        }

        public async Task InitializeAsync()
        {
            _logger.LogInformation("Cybernetics system initialized");
        }

        public async Task UpdateAsync(float deltaTime)
        {
            // Update all active implants
            var players = _playerManager.GetAllPlayers();
            
            foreach (var player in players)
            {
                await UpdatePlayerImplantsAsync(player, deltaTime);
            }
        }

        public async Task<ImplantInstallationResult> InstallImplantAsync(string playerId, CyberneticImplant implant)
        {
            try
            {
                var player = _playerManager.GetPlayer(playerId);
                if (player == null)
                {
                    return new ImplantInstallationResult 
                    { 
                        Success = false, 
                        Message = "Player not found" 
                    };
                }

                // Check if player has required skills
                if (!CanInstallImplant(player, implant))
                {
                    return new ImplantInstallationResult 
                    { 
                        Success = false, 
                        Message = "Insufficient skills or conflicting implants" 
                    };
                }

                // Check humanity cost
                if (player.CurrentHumanity < implant.HumanityCost)
                {
                    return new ImplantInstallationResult 
                    { 
                        Success = false, 
                        Message = "Insufficient humanity for implant installation" 
                    };
                }

                // Install the implant
                var playerImplants = _playerImplants.GetOrAdd(playerId, new List<CyberneticImplant>());
                playerImplants.Add(implant);

                // Apply humanity loss
                player.CurrentHumanity -= implant.HumanityCost;
                player.MaxHumanity = Math.Min(player.MaxHumanity, player.CurrentHumanity + 10f);

                // Check for cyberpsychosis
                bool causedPsychosis = player.CurrentHumanity < GameConstants.Cybernetics.HumanityLossThreshold;
                if (causedPsychosis)
                {
                    await ApplyCyberpsychosisAsync(player);
                }

                // Apply passive effects
                ApplyImplantEffects(player, implant);

                // Save changes
                _persistenceService.QueueSaveOperation(playerId, player);

                _logger.LogInformation("Implant {ImplantType} installed for player {PlayerId}. Humanity: {Humanity}", 
                    implant.ImplantType, playerId, player.CurrentHumanity);

                return new ImplantInstallationResult
                {
                    Success = true,
                    Message = "Implant installed successfully",
                    HumanityLoss = implant.HumanityCost,
                    CausedPsychosis = causedPsychosis
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error installing implant for player {PlayerId}", playerId);
                return new ImplantInstallationResult 
                { 
                    Success = false, 
                    Message = "Installation failed" 
                };
            }
        }

        public async Task<bool> RemoveImplantAsync(string playerId, string implantId)
        {
            try
            {
                var player = _playerManager.GetPlayer(playerId);
                if (player == null) return false;

                if (!_playerImplants.TryGetValue(playerId, out var implants))
                    return false;

                var implant = implants.FirstOrDefault(i => i.Id == implantId);
                if (implant == null) return false;

                // Remove implant
                implants.Remove(implant);

                // Remove passive effects
                RemoveImplantEffects(player, implant);

                // Partial humanity recovery (50% of original cost)
                player.CurrentHumanity = Math.Min(player.MaxHumanity, 
                    player.CurrentHumanity + (implant.HumanityCost * 0.5f));

                _persistenceService.QueueSaveOperation(playerId, player);

                _logger.LogInformation("Implant {ImplantId} removed from player {PlayerId}", 
                    implantId, playerId);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing implant {ImplantId} from player {PlayerId}", 
                    implantId, playerId);
                return false;
            }
        }

        public async Task<ImplantActivationResult> ActivateImplantAsync(string playerId, string implantId)
        {
            try
            {
                var player = _playerManager.GetPlayer(playerId);
                if (player == null)
                {
                    return new ImplantActivationResult 
                    { 
                        Success = false, 
                        Reason = "Player not found" 
                    };
                }

                if (!_playerImplants.TryGetValue(playerId, out var implants))
                {
                    return new ImplantActivationResult 
                    { 
                        Success = false, 
                        Reason = "No implants found" 
                    };
                }

                var implant = implants.FirstOrDefault(i => i.Id == implantId);
                if (implant == null)
                {
                    return new ImplantActivationResult 
                    { 
                        Success = false, 
                        Reason = "Implant not found" 
                    };
                }

                // Try to activate the implant
                var result = implant.Activate(player.CurrentSystemPower);
                if (result.Success)
                {
                    // Drain system power
                    player.CurrentSystemPower -= result.SystemPowerCost;
                    
                    // Apply active effects
                    ApplyActiveImplantEffects(player, implant);
                    
                    _persistenceService.QueueSaveOperation(playerId, player);
                    
                    _logger.LogDebug("Implant {ImplantId} activated for player {PlayerId}", 
                        implantId, playerId);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error activating implant {ImplantId} for player {PlayerId}", 
                    implantId, playerId);
                return new ImplantActivationResult 
                { 
                    Success = false, 
                    Reason = "Activation failed" 
                };
            }
        }

        public async Task<bool> DeactivateImplantAsync(string playerId, string implantId)
        {
            try
            {
                if (!_playerImplants.TryGetValue(playerId, out var implants))
                    return false;

                var implant = implants.FirstOrDefault(i => i.Id == implantId);
                if (implant == null) return false;

                var result = implant.Deactivate();
                if (result)
                {
                    var player = _playerManager.GetPlayer(playerId);
                    if (player != null)
                    {
                        RemoveActiveImplantEffects(player, implant);
                        _persistenceService.QueueSaveOperation(playerId, player);
                    }

                    _logger.LogDebug("Implant {ImplantId} deactivated for player {PlayerId}", 
                        implantId, playerId);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deactivating implant {ImplantId} for player {PlayerId}", 
                    implantId, playerId);
                return false;
            }
        }

        public async Task ProcessEMPDamageAsync(string playerId, float empDamage)
        {
            try
            {
                var player = _playerManager.GetPlayer(playerId);
                if (player == null) return;

                if (!_playerImplants.TryGetValue(playerId, out var implants))
                    return;

                foreach (var implant in implants)
                {
                    var result = implant.ProcessEMPDamage(empDamage);
                    
                    if (result.WasDisabled)
                    {
                        RemoveActiveImplantEffects(player, implant);
                        _logger.LogInformation("Implant {ImplantType} disabled by EMP for player {PlayerId}", 
                            implant.ImplantType, playerId);
                    }

                    // Apply system power drain
                    player.CurrentSystemPower = Math.Max(0, 
                        player.CurrentSystemPower - GameConstants.Cybernetics.EMPSystemPowerDrain);
                }

                _persistenceService.QueueSaveOperation(playerId, player);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing EMP damage for player {PlayerId}", playerId);
            }
        }

        public async Task ProcessImplantRejectionAsync(string playerId)
        {
            try
            {
                if (!_playerImplants.TryGetValue(playerId, out var implants))
                    return;

                var rejectedImplants = new List<CyberneticImplant>();

                foreach (var implant in implants)
                {
                    if (implant.CheckRejection())
                    {
                        rejectedImplants.Add(implant);
                    }
                }

                if (rejectedImplants.Any())
                {
                    var player = _playerManager.GetPlayer(playerId);
                    if (player != null)
                    {
                        foreach (var implant in rejectedImplants)
                        {
                            implants.Remove(implant);
                            RemoveImplantEffects(player, implant);
                            
                            // Apply rejection damage
                            await _playerManager.DamagePlayerAsync(playerId, 25f, "implant_rejection");
                        }

                        _persistenceService.QueueSaveOperation(playerId, player);
                        
                        _logger.LogWarning("Player {PlayerId} rejected {Count} implants", 
                            playerId, rejectedImplants.Count);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing implant rejection for player {PlayerId}", playerId);
            }
        }

        private async Task UpdatePlayerImplantsAsync(Player player, float deltaTime)
        {
            if (!_playerImplants.TryGetValue(player.PlayerId, out var implants))
                return;

            foreach (var implant in implants.Where(i => i.IsActive))
            {
                // Drain system power for active implants
                player.CurrentSystemPower = Math.Max(0, 
                    player.CurrentSystemPower - (implant.SystemPowerDrain * deltaTime));

                // Deactivate if no power
                if (player.CurrentSystemPower <= 0 && implant.IsActive)
                {
                    implant.Deactivate();
                    RemoveActiveImplantEffects(player, implant);
                    
                    _logger.LogDebug("Implant {ImplantType} auto-deactivated due to power loss for player {PlayerId}", 
                        implant.ImplantType, player.PlayerId);
                }
            }
        }

        private bool CanInstallImplant(Player player, CyberneticImplant implant)
        {
            // Check skill requirements
            foreach (var requirement in implant.SkillRequirements)
            {
                if (!player.HasSkill(requirement.Key, requirement.Value))
                    return false;
            }

            // Check level requirement
            if (player.Level < implant.LevelRequirement)
                return false;

            // Check for slot conflicts
            if (_playerImplants.TryGetValue(player.PlayerId, out var existingImplants))
            {
                if (existingImplants.Any(i => i.Slot == implant.Slot))
                    return false;
            }

            return true;
        }

        private void ApplyImplantEffects(Player player, CyberneticImplant implant)
        {
            // Apply attribute modifiers
            foreach (var modifier in implant.AttributeModifiers)
            {
                // This would modify player attributes based on the implant
                // Implementation depends on how attributes are stored
            }

            // Apply passive status effects
            foreach (var effect in implant.PassiveEffects)
            {
                player.ActiveStatusEffects.Add(effect);
            }
        }

        private void RemoveImplantEffects(Player player, CyberneticImplant implant)
        {
            // Remove passive status effects
            foreach (var effect in implant.PassiveEffects)
            {
                player.ActiveStatusEffects.RemoveAll(e => e.Id == effect.Id);
            }
        }

        private void ApplyActiveImplantEffects(Player player, CyberneticImplant implant)
        {
            // Apply temporary active effects
            // This would depend on the specific implant type
        }

        private void RemoveActiveImplantEffects(Player player, CyberneticImplant implant)
        {
            // Remove temporary active effects
            // This would depend on the specific implant type
        }

        private async Task ApplyCyberpsychosisAsync(Player player)
        {
            var psychosisEffect = new StatusEffect
            {
                Type = StatusEffectType.CyberPsychosis,
                Name = "Cyberpsychosis",
                Description = "Mental instability from excessive cybernetic augmentation",
                IsPermanent = true,
                DamageModifier = -0.1f, // 10% damage reduction
                MoveSpeedModifier = 0.2f // 20% speed increase
            };

            player.ActiveStatusEffects.Add(psychosisEffect);
            
            _logger.LogWarning("Player {PlayerId} ({CharacterName}) has developed cyberpsychosis!", 
                player.PlayerId, player.CharacterName);
        }
    }
}
