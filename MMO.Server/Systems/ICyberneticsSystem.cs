using MMO.Shared.Models.Characters;
using MMO.Shared.Models.Systems;
using MMO.Shared.Models.Items;

namespace MMO.Server.Systems
{
    /// <summary>
    /// Handles cybernetic implant systems and abilities
    /// </summary>
    public interface ICyberneticsSystem
    {
        Task InitializeAsync();
        Task UpdateAsync(float deltaTime);

        /// <summary>
        /// Installs a cybernetic implant in a player
        /// </summary>
        Task<ImplantInstallationResult> InstallImplantAsync(string playerId, CyberneticImplant implant);

        /// <summary>
        /// Removes a cybernetic implant from a player
        /// </summary>
        Task<bool> RemoveImplantAsync(string playerId, string implantId);

        /// <summary>
        /// Activates a cybernetic implant
        /// </summary>
        Task<ImplantActivationResult> ActivateImplantAsync(string playerId, string implantId);

        /// <summary>
        /// Deactivates a cybernetic implant
        /// </summary>
        Task<bool> DeactivateImplantAsync(string playerId, string implantId);

        /// <summary>
        /// Processes EMP damage to player's implants
        /// </summary>
        Task ProcessEMPDamageAsync(string playerId, float empDamage);

        /// <summary>
        /// Checks for implant rejection
        /// </summary>
        Task ProcessImplantRejectionAsync(string playerId);
    }

    public class ImplantInstallationResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public float HumanityLoss { get; set; }
        public bool CausedPsychosis { get; set; }
    }
}
