using MMO.Shared.Models.Characters;
using MMO.Shared.Models.Common;
using MMO.Shared.Models.Items;

namespace MMO.Server.Systems
{
    /// <summary>
    /// Handles all combat-related logic
    /// </summary>
    public interface ICombatSystem
    {
        /// <summary>
        /// Initializes the combat system
        /// </summary>
        Task InitializeAsync();
        
        /// <summary>
        /// Updates the combat system
        /// </summary>
        Task UpdateAsync(float deltaTime);
        
        /// <summary>
        /// Processes a weapon fire event
        /// </summary>
        Task<CombatResult> ProcessWeaponFireAsync(string attackerId, string weaponId, Vector3 targetPosition, string? targetId = null);
        
        /// <summary>
        /// Calculates damage based on hit location and armor
        /// </summary>
        float CalculateDamage(float baseDamage, DamageType damageType, HitLocation hitLocation, float armorRating);
        
        /// <summary>
        /// Applies damage to a target
        /// </summary>
        Task<bool> ApplyDamageAsync(string targetId, float damage, DamageType damageType, string sourceId);
        
        /// <summary>
        /// Checks if a player is in combat
        /// </summary>
        bool IsPlayerInCombat(string playerId);
        
        /// <summary>
        /// Starts combat for a player
        /// </summary>
        Task StartCombatAsync(string playerId);
        
        /// <summary>
        /// Ends combat for a player
        /// </summary>
        Task EndCombatAsync(string playerId);
    }
    
    public class CombatResult
    {
        public bool Success { get; set; }
        public bool Hit { get; set; }
        public float Damage { get; set; }
        public DamageType DamageType { get; set; }
        public HitLocation HitLocation { get; set; }
        public bool IsCritical { get; set; }
        public bool IsHeadshot { get; set; }
        public bool TargetKilled { get; set; }
        public string Message { get; set; } = string.Empty;
    }
    
    public enum HitLocation
    {
        Miss,
        Head,
        Torso,
        LeftArm,
        RightArm,
        LeftLeg,
        RightLeg
    }
}
