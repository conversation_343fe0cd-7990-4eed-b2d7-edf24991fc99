using Microsoft.Extensions.Logging;
using MMO.Server.Managers;
using MMO.Server.Services;
using MMO.Shared.Models.Characters;
using MMO.Shared.Models.Common;
using MMO.Shared.Models.Systems;
using System.Collections.Concurrent;

namespace MMO.Server.Systems
{
    /// <summary>
    /// Handles guilds, housing, and social features
    /// </summary>
    public class SocialSystem : ISocialSystem
    {
        private readonly ILogger<SocialSystem> _logger;
        private readonly IPlayerManager _playerManager;
        private readonly IPersistenceService _persistenceService;
        private readonly ConcurrentDictionary<string, Guild> _guilds;
        private readonly ConcurrentDictionary<string, PlayerHouse> _houses;
        private readonly ConcurrentDictionary<string, List<string>> _friendships;

        public SocialSystem(
            ILogger<SocialSystem> logger,
            IPlayerManager playerManager,
            IPersistenceService persistenceService)
        {
            _logger = logger;
            _playerManager = playerManager;
            _persistenceService = persistenceService;
            _guilds = new ConcurrentDictionary<string, Guild>();
            _houses = new ConcurrentDictionary<string, PlayerHouse>();
            _friendships = new ConcurrentDictionary<string, List<string>>();
        }

        public async Task InitializeAsync()
        {
            // Load guilds and houses from database
            await LoadGuildsAsync();
            await LoadHousesAsync();
            _logger.LogInformation("Social system initialized with {GuildCount} guilds and {HouseCount} houses", 
                _guilds.Count, _houses.Count);
        }

        public async Task UpdateAsync(float deltaTime)
        {
            // Process guild maintenance
            await ProcessGuildMaintenanceAsync();
            
            // Process house maintenance
            await ProcessHouseMaintenanceAsync();
        }

        public async Task<GuildCreationResult> CreateGuildAsync(string founderId, string guildName, string description)
        {
            try
            {
                var founder = _playerManager.GetPlayer(founderId);
                if (founder == null)
                {
                    return new GuildCreationResult { Success = false, Message = "Player not found" };
                }

                if (!string.IsNullOrEmpty(founder.GuildId))
                {
                    return new GuildCreationResult { Success = false, Message = "Player is already in a guild" };
                }

                if (founder.Credits < GameConstants.Guilds.GuildCreationCost)
                {
                    return new GuildCreationResult { Success = false, Message = "Insufficient credits" };
                }

                // Check if guild name is available
                if (_guilds.Values.Any(g => g.Name.Equals(guildName, StringComparison.OrdinalIgnoreCase)))
                {
                    return new GuildCreationResult { Success = false, Message = "Guild name already taken" };
                }

                // Create guild
                var guild = new Guild
                {
                    Name = guildName,
                    Description = description,
                    LeaderId = founderId,
                    LeaderName = founder.CharacterName
                };

                // Add founder as leader
                guild.Members.Add(new GuildMember
                {
                    PlayerId = founderId,
                    PlayerName = founder.CharacterName,
                    Rank = GuildRank.Leader
                });

                _guilds.TryAdd(guild.Id, guild);

                // Update player
                founder.GuildId = guild.Id;
                founder.GuildRank = GuildRank.Leader;
                founder.Credits -= GameConstants.Guilds.GuildCreationCost;

                // Save changes
                _persistenceService.QueueSaveOperation(founderId, founder);
                await _persistenceService.SaveWorldObjectAsync($"guild_{guild.Id}", guild);

                _logger.LogInformation("Player {PlayerId} created guild {GuildName} ({GuildId})", 
                    founderId, guildName, guild.Id);

                return new GuildCreationResult
                {
                    Success = true,
                    Message = "Guild created successfully",
                    GuildId = guild.Id,
                    CreationCost = GameConstants.Guilds.GuildCreationCost
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating guild {GuildName} for player {PlayerId}", guildName, founderId);
                return new GuildCreationResult { Success = false, Message = "Guild creation failed" };
            }
        }

        public async Task<bool> DisbandGuildAsync(string guildId, string leaderId)
        {
            try
            {
                if (!_guilds.TryGetValue(guildId, out var guild))
                    return false;

                if (guild.LeaderId != leaderId)
                    return false;

                // Remove all members from guild
                foreach (var member in guild.Members)
                {
                    var player = _playerManager.GetPlayer(member.PlayerId);
                    if (player != null)
                    {
                        player.GuildId = string.Empty;
                        player.GuildRank = GuildRank.None;
                        _persistenceService.QueueSaveOperation(member.PlayerId, player);
                    }
                }

                guild.Status = GuildStatus.Disbanded;
                _guilds.TryRemove(guildId, out _);

                _logger.LogInformation("Guild {GuildName} ({GuildId}) disbanded by {LeaderId}", 
                    guild.Name, guildId, leaderId);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error disbanding guild {GuildId}", guildId);
                return false;
            }
        }

        public async Task<bool> InvitePlayerToGuildAsync(string guildId, string inviterId, string inviteeId)
        {
            try
            {
                if (!_guilds.TryGetValue(guildId, out var guild))
                    return false;

                if (!guild.HasPermission(inviterId, GuildPermission.InviteMembers))
                    return false;

                var invitee = _playerManager.GetPlayer(inviteeId);
                if (invitee == null || !string.IsNullOrEmpty(invitee.GuildId))
                    return false;

                if (!guild.HasSpace)
                    return false;

                // TODO: Send guild invitation to player
                // For now, just log the invitation
                _logger.LogInformation("Player {InviterId} invited {InviteeId} to guild {GuildId}", 
                    inviterId, inviteeId, guildId);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error inviting player {InviteeId} to guild {GuildId}", inviteeId, guildId);
                return false;
            }
        }

        public async Task<bool> AcceptGuildInviteAsync(string playerId, string guildId)
        {
            try
            {
                var player = _playerManager.GetPlayer(playerId);
                if (player == null || !string.IsNullOrEmpty(player.GuildId))
                    return false;

                if (!_guilds.TryGetValue(guildId, out var guild))
                    return false;

                if (!guild.HasSpace)
                    return false;

                // Add player to guild
                guild.Members.Add(new GuildMember
                {
                    PlayerId = playerId,
                    PlayerName = player.CharacterName,
                    Rank = GuildRank.Member
                });

                player.GuildId = guildId;
                player.GuildRank = GuildRank.Member;

                _persistenceService.QueueSaveOperation(playerId, player);
                await _persistenceService.SaveWorldObjectAsync($"guild_{guildId}", guild);

                _logger.LogInformation("Player {PlayerId} joined guild {GuildId}", playerId, guildId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error accepting guild invite for player {PlayerId}", playerId);
                return false;
            }
        }

        public async Task<bool> LeaveGuildAsync(string playerId)
        {
            try
            {
                var player = _playerManager.GetPlayer(playerId);
                if (player == null || string.IsNullOrEmpty(player.GuildId))
                    return false;

                if (!_guilds.TryGetValue(player.GuildId, out var guild))
                    return false;

                // Can't leave if you're the leader and there are other members
                if (player.GuildRank == GuildRank.Leader && guild.Members.Count > 1)
                    return false;

                // Remove from guild
                guild.Members.RemoveAll(m => m.PlayerId == playerId);
                player.GuildId = string.Empty;
                player.GuildRank = GuildRank.None;

                _persistenceService.QueueSaveOperation(playerId, player);
                await _persistenceService.SaveWorldObjectAsync($"guild_{guild.Id}", guild);

                _logger.LogInformation("Player {PlayerId} left guild {GuildId}", playerId, guild.Id);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error leaving guild for player {PlayerId}", playerId);
                return false;
            }
        }

        public async Task<bool> KickPlayerFromGuildAsync(string guildId, string kickerId, string targetId)
        {
            try
            {
                if (!_guilds.TryGetValue(guildId, out var guild))
                    return false;

                if (!guild.HasPermission(kickerId, GuildPermission.KickMembers))
                    return false;

                var target = guild.GetMember(targetId);
                if (target == null || target.Rank >= guild.GetMember(kickerId)?.Rank)
                    return false;

                // Remove from guild
                guild.Members.RemoveAll(m => m.PlayerId == targetId);

                var targetPlayer = _playerManager.GetPlayer(targetId);
                if (targetPlayer != null)
                {
                    targetPlayer.GuildId = string.Empty;
                    targetPlayer.GuildRank = GuildRank.None;
                    _persistenceService.QueueSaveOperation(targetId, targetPlayer);
                }

                await _persistenceService.SaveWorldObjectAsync($"guild_{guildId}", guild);

                _logger.LogInformation("Player {TargetId} kicked from guild {GuildId} by {KickerId}", 
                    targetId, guildId, kickerId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error kicking player {TargetId} from guild {GuildId}", targetId, guildId);
                return false;
            }
        }

        public async Task<bool> PromotePlayerAsync(string guildId, string promoterId, string targetId)
        {
            try
            {
                if (!_guilds.TryGetValue(guildId, out var guild))
                    return false;

                if (!guild.HasPermission(promoterId, GuildPermission.ManageRanks))
                    return false;

                var target = guild.GetMember(targetId);
                if (target == null || target.Rank >= GuildRank.Officer)
                    return false;

                // Promote player
                target.Rank = GuildRank.Officer;

                var targetPlayer = _playerManager.GetPlayer(targetId);
                if (targetPlayer != null)
                {
                    targetPlayer.GuildRank = GuildRank.Officer;
                    _persistenceService.QueueSaveOperation(targetId, targetPlayer);
                }

                await _persistenceService.SaveWorldObjectAsync($"guild_{guildId}", guild);

                _logger.LogInformation("Player {TargetId} promoted to Officer in guild {GuildId} by {PromoterId}", 
                    targetId, guildId, promoterId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error promoting player {TargetId} in guild {GuildId}", targetId, guildId);
                return false;
            }
        }

        public async Task<bool> DemotePlayerAsync(string guildId, string demoterId, string targetId)
        {
            try
            {
                if (!_guilds.TryGetValue(guildId, out var guild))
                    return false;

                if (!guild.HasPermission(demoterId, GuildPermission.ManageRanks))
                    return false;

                var target = guild.GetMember(targetId);
                if (target == null || target.Rank <= GuildRank.Member)
                    return false;

                // Demote player
                target.Rank = GuildRank.Member;

                var targetPlayer = _playerManager.GetPlayer(targetId);
                if (targetPlayer != null)
                {
                    targetPlayer.GuildRank = GuildRank.Member;
                    _persistenceService.QueueSaveOperation(targetId, targetPlayer);
                }

                await _persistenceService.SaveWorldObjectAsync($"guild_{guildId}", guild);

                _logger.LogInformation("Player {TargetId} demoted to Member in guild {GuildId} by {DemoterId}", 
                    targetId, guildId, demoterId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error demoting player {TargetId} in guild {GuildId}", targetId, guildId);
                return false;
            }
        }

        public async Task<HousingResult> PurchaseHouseAsync(string playerId, string houseType, string location)
        {
            try
            {
                var player = _playerManager.GetPlayer(playerId);
                if (player == null)
                {
                    return new HousingResult { Success = false, Message = "Player not found" };
                }

                if (player.OwnedHouseIds.Count >= GameConstants.Housing.MaxHousesPerPlayer)
                {
                    return new HousingResult { Success = false, Message = "Maximum houses owned" };
                }

                // Calculate house cost based on type and location
                decimal cost = CalculateHouseCost(houseType, location);

                if (player.Credits < cost)
                {
                    return new HousingResult { Success = false, Message = "Insufficient credits" };
                }

                // Create house
                var house = new PlayerHouse
                {
                    OwnerId = playerId,
                    OwnerName = player.CharacterName,
                    Type = Enum.Parse<HouseType>(houseType, true),
                    Location = location,
                    PurchasePrice = cost,
                    MaintenanceCost = GameConstants.Housing.WeeklyMaintenanceCost
                };

                _houses.TryAdd(house.Id, house);
                player.OwnedHouseIds.Add(house.Id);
                player.Credits -= cost;

                _persistenceService.QueueSaveOperation(playerId, player);
                await _persistenceService.SaveWorldObjectAsync($"house_{house.Id}", house);

                _logger.LogInformation("Player {PlayerId} purchased {HouseType} in {Location} for {Cost} credits", 
                    playerId, houseType, location, cost);

                return new HousingResult
                {
                    Success = true,
                    Message = "House purchased successfully",
                    HouseId = house.Id,
                    Cost = cost
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error purchasing house for player {PlayerId}", playerId);
                return new HousingResult { Success = false, Message = "Purchase failed" };
            }
        }

        public async Task<bool> SellHouseAsync(string playerId, string houseId)
        {
            try
            {
                if (!_houses.TryGetValue(houseId, out var house))
                    return false;

                if (house.OwnerId != playerId)
                    return false;

                var player = _playerManager.GetPlayer(playerId);
                if (player == null)
                    return false;

                // Calculate sell price (75% of purchase price)
                decimal sellPrice = house.PurchasePrice * 0.75m;

                // Remove house
                _houses.TryRemove(houseId, out _);
                player.OwnedHouseIds.Remove(houseId);
                player.Credits += sellPrice;

                _persistenceService.QueueSaveOperation(playerId, player);

                _logger.LogInformation("Player {PlayerId} sold house {HouseId} for {SellPrice} credits", 
                    playerId, houseId, sellPrice);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error selling house {HouseId} for player {PlayerId}", houseId, playerId);
                return false;
            }
        }

        public async Task<bool> UpgradeHouseAsync(string playerId, string houseId, string upgradeType)
        {
            try
            {
                if (!_houses.TryGetValue(houseId, out var house))
                    return false;

                if (house.OwnerId != playerId)
                    return false;

                var player = _playerManager.GetPlayer(playerId);
                if (player == null)
                    return false;

                // Calculate upgrade cost
                decimal upgradeCost = CalculateUpgradeCost(house, upgradeType);

                if (player.Credits < upgradeCost)
                    return false;

                // Apply upgrade
                var upgrade = new HouseUpgrade
                {
                    Type = Enum.Parse<HouseUpgradeType>(upgradeType, true),
                    Name = upgradeType,
                    Cost = upgradeCost
                };

                house.Upgrades.Add(upgrade);
                player.Credits -= upgradeCost;

                _persistenceService.QueueSaveOperation(playerId, player);
                await _persistenceService.SaveWorldObjectAsync($"house_{houseId}", house);

                _logger.LogInformation("Player {PlayerId} upgraded house {HouseId} with {UpgradeType}", 
                    playerId, houseId, upgradeType);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error upgrading house {HouseId} for player {PlayerId}", houseId, playerId);
                return false;
            }
        }

        public async Task<bool> SetHousePermissionsAsync(string playerId, string houseId, HousePermissions permissions)
        {
            try
            {
                if (!_houses.TryGetValue(houseId, out var house))
                    return false;

                if (house.OwnerId != playerId)
                    return false;

                house.Permissions = permissions;
                await _persistenceService.SaveWorldObjectAsync($"house_{houseId}", house);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting house permissions for {HouseId}", houseId);
                return false;
            }
        }

        public async Task<List<PlayerHouse>> GetPlayerHousesAsync(string playerId)
        {
            return _houses.Values
                .Where(h => h.OwnerId == playerId)
                .OrderBy(h => h.PurchasedAt)
                .ToList();
        }

        public async Task<bool> SendFriendRequestAsync(string senderId, string receiverId)
        {
            // TODO: Implement friend request system
            _logger.LogInformation("Friend request sent from {SenderId} to {ReceiverId}", senderId, receiverId);
            return true;
        }

        public async Task<bool> AcceptFriendRequestAsync(string playerId, string friendId)
        {
            // TODO: Implement friend acceptance
            var playerFriends = _friendships.GetOrAdd(playerId, new List<string>());
            var friendFriends = _friendships.GetOrAdd(friendId, new List<string>());

            if (!playerFriends.Contains(friendId))
                playerFriends.Add(friendId);
            if (!friendFriends.Contains(playerId))
                friendFriends.Add(playerId);

            _logger.LogInformation("Players {PlayerId} and {FriendId} are now friends", playerId, friendId);
            return true;
        }

        public async Task<bool> RemoveFriendAsync(string playerId, string friendId)
        {
            if (_friendships.TryGetValue(playerId, out var playerFriends))
                playerFriends.Remove(friendId);
            if (_friendships.TryGetValue(friendId, out var friendFriends))
                friendFriends.Remove(playerId);

            _logger.LogInformation("Friendship removed between {PlayerId} and {FriendId}", playerId, friendId);
            return true;
        }

        public async Task<List<string>> GetPlayerFriendsAsync(string playerId)
        {
            return _friendships.GetValueOrDefault(playerId, new List<string>());
        }

        private async Task LoadGuildsAsync()
        {
            // TODO: Load from database
        }

        private async Task LoadHousesAsync()
        {
            // TODO: Load from database
        }

        private async Task ProcessGuildMaintenanceAsync()
        {
            // TODO: Process weekly guild maintenance costs
        }

        private async Task ProcessHouseMaintenanceAsync()
        {
            // TODO: Process weekly house maintenance costs
        }

        private decimal CalculateHouseCost(string houseType, string location)
        {
            var basePrice = GameConstants.Housing.BaseHouseCost;
            
            // Adjust price based on house type
            var multiplier = houseType.ToLower() switch
            {
                "apartment" => 1f,
                "house" => 2f,
                "mansion" => 5f,
                "penthouse" => 3f,
                "warehouse" => 1.5f,
                "workshop" => 2.5f,
                _ => 1f
            };

            return basePrice * (decimal)multiplier;
        }

        private decimal CalculateUpgradeCost(PlayerHouse house, string upgradeType)
        {
            // Base upgrade cost
            return 5000m * (house.Level + 1);
        }
    }
}
