{"profiles": {"MMO.Server": {"commandName": "Project", "dotnetRunMessages": true, "environmentVariables": {"DOTNET_ENVIRONMENT": "Development", "ASPNETCORE_ENVIRONMENT": "Development"}}, "MMO.Server (Debug)": {"commandName": "Project", "dotnetRunMessages": true, "environmentVariables": {"DOTNET_ENVIRONMENT": "Development", "ASPNETCORE_ENVIRONMENT": "Development", "GameServer__ZoneId": "zone-debug-1", "GameServer__ServerPort": "7778", "GameServer__EnableDebugLogging": "true", "DynamoDb__ServiceURL": "http://localhost:8000", "DynamoDb__UseFauxCredentials": "true", "DynamoDb__EnableRequestLogging": "true", "DynamoDb__RequestLogLevel": "Debug"}}, "MMO.Server (Production)": {"commandName": "Project", "dotnetRunMessages": true, "environmentVariables": {"DOTNET_ENVIRONMENT": "Production", "ASPNETCORE_ENVIRONMENT": "Production", "GameServer__ZoneId": "zone-prod-1", "GameServer__ServerPort": "7777", "GameServer__EnableDebugLogging": "false"}}, "Docker": {"commandName": "<PERSON>er"}}}