using MMO.Shared.Models.Characters;
using MMO.Shared.Models.Common;

namespace MMO.Server.Managers
{
    /// <summary>
    /// Manages all players connected to this server
    /// </summary>
    public interface IPlayerManager
    {
        /// <summary>
        /// Initializes the player manager
        /// </summary>
        Task InitializeAsync();
        
        /// <summary>
        /// Shuts down the player manager
        /// </summary>
        Task ShutdownAsync();
        
        /// <summary>
        /// Updates all players
        /// </summary>
        Task UpdateAsync(float deltaTime);
        
        /// <summary>
        /// Adds a player to the server
        /// </summary>
        Task<bool> AddPlayerAsync(Player player);
        
        /// <summary>
        /// Removes a player from the server
        /// </summary>
        Task<bool> RemovePlayerAsync(string playerId);
        
        /// <summary>
        /// Gets a player by ID
        /// </summary>
        Player? GetPlayer(string playerId);
        
        /// <summary>
        /// Gets all players in the server
        /// </summary>
        IEnumerable<Player> GetAllPlayers();
        
        /// <summary>
        /// Gets players within a certain distance of a position
        /// </summary>
        IEnumerable<Player> GetPlayersNearPosition(Vector3 position, float radius);
        
        /// <summary>
        /// Gets the current player count
        /// </summary>
        int GetPlayerCount();
        
        /// <summary>
        /// Updates a player's position
        /// </summary>
        Task UpdatePlayerPositionAsync(string playerId, Vector3 position, Vector3 rotation);
        
        /// <summary>
        /// Applies damage to a player
        /// </summary>
        Task<bool> DamagePlayerAsync(string playerId, float damage, string sourceId = "");
        
        /// <summary>
        /// Heals a player
        /// </summary>
        Task HealPlayerAsync(string playerId, float amount);
        
        /// <summary>
        /// Adds experience to a player
        /// </summary>
        Task<bool> AddExperienceAsync(string playerId, long amount);
    }
}
