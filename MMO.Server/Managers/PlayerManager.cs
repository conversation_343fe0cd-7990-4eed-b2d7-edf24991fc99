using Microsoft.Extensions.Logging;
using MMO.Server.Services;
using MMO.Shared.Models.Characters;
using MMO.Shared.Models.Common;
using System.Collections.Concurrent;

namespace MMO.Server.Managers
{
    /// <summary>
    /// Manages all players connected to this server
    /// </summary>
    public class PlayerManager : IPlayerManager
    {
        private readonly ILogger<PlayerManager> _logger;
        private readonly IPersistenceService _persistenceService;
        private readonly ConcurrentDictionary<string, Player> _players;
        private readonly Timer _saveTimer;

        public PlayerManager(ILogger<PlayerManager> logger, IPersistenceService persistenceService)
        {
            _logger = logger;
            _persistenceService = persistenceService;
            _players = new ConcurrentDictionary<string, Player>();
            
            // Auto-save players every 30 seconds
            _saveTimer = new Timer(AutoSavePlayers, null, Timeout.Infinite, Timeout.Infinite);
        }

        public async Task InitializeAsync()
        {
            _logger.LogInformation("Initializing player manager...");
            
            // Start auto-save timer
            _saveTimer.Change(TimeSpan.FromSeconds(30), TimeSpan.FromSeconds(30));
            
            _logger.LogInformation("Player manager initialized successfully");
        }

        public async Task ShutdownAsync()
        {
            _logger.LogInformation("Shutting down player manager...");
            
            // Stop auto-save timer
            _saveTimer?.Change(Timeout.Infinite, Timeout.Infinite);
            
            // Save all players before shutdown
            await SaveAllPlayersAsync();
            
            _logger.LogInformation("Player manager shutdown completed");
        }

        public async Task UpdateAsync(float deltaTime)
        {
            // Update all players
            var updateTasks = _players.Values.Select(player => UpdatePlayerAsync(player, deltaTime));
            await Task.WhenAll(updateTasks);
        }

        public async Task<bool> AddPlayerAsync(Player player)
        {
            try
            {
                if (_players.TryAdd(player.PlayerId, player))
                {
                    player.Status = PlayerStatus.Online;
                    player.LastLoginAt = DateTime.UtcNow;
                    
                    _logger.LogInformation("Player {PlayerId} ({CharacterName}) joined the server", 
                        player.PlayerId, player.CharacterName);
                    
                    // Queue immediate save
                    _persistenceService.QueueSaveOperation(player.PlayerId, player);
                    
                    return true;
                }
                
                _logger.LogWarning("Failed to add player {PlayerId} - already exists", player.PlayerId);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding player {PlayerId}", player.PlayerId);
                return false;
            }
        }

        public async Task<bool> RemovePlayerAsync(string playerId)
        {
            try
            {
                if (_players.TryRemove(playerId, out var player))
                {
                    player.Status = PlayerStatus.Offline;
                    
                    // Save player data before removal
                    await _persistenceService.SavePlayerAsync(player);
                    
                    _logger.LogInformation("Player {PlayerId} ({CharacterName}) left the server", 
                        player.PlayerId, player.CharacterName);
                    
                    return true;
                }
                
                _logger.LogWarning("Failed to remove player {PlayerId} - not found", playerId);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing player {PlayerId}", playerId);
                return false;
            }
        }

        public Player? GetPlayer(string playerId)
        {
            _players.TryGetValue(playerId, out var player);
            return player;
        }

        public IEnumerable<Player> GetAllPlayers()
        {
            return _players.Values.ToList();
        }

        public IEnumerable<Player> GetPlayersNearPosition(Vector3 position, float radius)
        {
            var radiusSquared = radius * radius;
            
            return _players.Values.Where(player => 
                Vector3.SqrDistance(player.Position, position) <= radiusSquared);
        }

        public int GetPlayerCount()
        {
            return _players.Count;
        }

        public async Task UpdatePlayerPositionAsync(string playerId, Vector3 position, Vector3 rotation)
        {
            if (_players.TryGetValue(playerId, out var player))
            {
                player.Position = position;
                player.Rotation = rotation;
                
                // Queue save for batch processing
                _persistenceService.QueueSaveOperation(playerId, player);
            }
        }

        public async Task<bool> DamagePlayerAsync(string playerId, float damage, string sourceId = "")
        {
            if (_players.TryGetValue(playerId, out var player))
            {
                bool died = player.TakeDamage(damage);
                
                _logger.LogDebug("Player {PlayerId} took {Damage} damage from {SourceId}. Health: {Health}/{MaxHealth}", 
                    playerId, damage, sourceId, player.CurrentHealth, player.MaxHealth);
                
                if (died)
                {
                    _logger.LogInformation("Player {PlayerId} ({CharacterName}) died", 
                        player.PlayerId, player.CharacterName);
                }
                
                // Queue save
                _persistenceService.QueueSaveOperation(playerId, player);
                
                return died;
            }
            
            return false;
        }

        public async Task HealPlayerAsync(string playerId, float amount)
        {
            if (_players.TryGetValue(playerId, out var player))
            {
                player.Heal(amount);
                
                _logger.LogDebug("Player {PlayerId} healed for {Amount}. Health: {Health}/{MaxHealth}", 
                    playerId, amount, player.CurrentHealth, player.MaxHealth);
                
                // Queue save
                _persistenceService.QueueSaveOperation(playerId, player);
            }
        }

        public async Task<bool> AddExperienceAsync(string playerId, long amount)
        {
            if (_players.TryGetValue(playerId, out var player))
            {
                bool leveledUp = player.AddExperience(amount);
                
                _logger.LogDebug("Player {PlayerId} gained {Experience} XP. Total: {TotalXP}, Level: {Level}", 
                    playerId, amount, player.ExperiencePoints, player.Level);
                
                if (leveledUp)
                {
                    _logger.LogInformation("Player {PlayerId} ({CharacterName}) leveled up to level {Level}!", 
                        player.PlayerId, player.CharacterName, player.Level);
                }
                
                // Queue save
                _persistenceService.QueueSaveOperation(playerId, player);
                
                return leveledUp;
            }
            
            return false;
        }

        private async Task UpdatePlayerAsync(Player player, float deltaTime)
        {
            try
            {
                // Update status effects
                for (int i = player.ActiveStatusEffects.Count - 1; i >= 0; i--)
                {
                    var effect = player.ActiveStatusEffects[i];
                    if (effect.HasExpired)
                    {
                        player.ActiveStatusEffects.RemoveAt(i);
                        _logger.LogDebug("Status effect {EffectName} expired for player {PlayerId}", 
                            effect.Name, player.PlayerId);
                    }
                }
                
                // Regenerate system power
                if (player.CurrentSystemPower < player.MaxSystemPower)
                {
                    var regenAmount = GameConstants.Cybernetics.SystemPowerRegenRate * deltaTime;
                    player.CurrentSystemPower = Math.Min(player.MaxSystemPower, 
                        player.CurrentSystemPower + regenAmount);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating player {PlayerId}", player.PlayerId);
            }
        }

        private async void AutoSavePlayers(object? state)
        {
            try
            {
                await SaveAllPlayersAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during auto-save");
            }
        }

        private async Task SaveAllPlayersAsync()
        {
            if (_players.IsEmpty) return;
            
            _logger.LogDebug("Auto-saving {Count} players", _players.Count);
            
            var saveTasks = _players.Values.Select(player => 
                _persistenceService.SavePlayerAsync(player));
            
            await Task.WhenAll(saveTasks);
            
            _logger.LogDebug("Auto-save completed for {Count} players", _players.Count);
        }

        public void Dispose()
        {
            _saveTimer?.Dispose();
        }
    }
}
