{"Logging": {"LogLevel": {"Default": "Debug", "Microsoft": "Information", "Microsoft.Hosting.Lifetime": "Information", "MMO.Server": "Debug"}}, "DynamoDb": {"PlayersTableName": "reapers-passing-players-dev", "WorldObjectsTableName": "reapers-passing-world-objects-dev", "ZoneRegistryTableName": "reapers-passing-zone-registry-dev", "BatchSaveIntervalSeconds": 2, "MaxBatchSize": 10, "Region": "USEast1", "ServiceURL": "http://localhost:8000"}, "GameServer": {"ZoneId": "zone-dev-1", "ServerPort": 7778, "HeartbeatIntervalSeconds": 15, "GameLoopUpdateRate": 10.0, "MaxPlayers": 10, "Region": "us-east-1", "EnableDebugLogging": true}}