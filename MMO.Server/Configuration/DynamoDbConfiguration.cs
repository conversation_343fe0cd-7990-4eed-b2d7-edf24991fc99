namespace MMO.Server.Configuration
{
    /// <summary>
    /// Configuration settings for DynamoDB services
    /// </summary>
    public class DynamoDbConfiguration
    {
        /// <summary>
        /// 
        /// </summary>
        public const string SectionName = "DynamoDb";

        /// <summary>
        /// URL for DynamoDB service (for local development, use http://localhost:8000)
        /// </summary>
        public string? ServiceURL { get; set; }

        /// <summary>
        /// Name of the DynamoDB table for storing player data
        /// </summary>
        public string PlayersTableName { get; set; } = "reapers-passing-players";

        /// <summary>
        /// Name of the DynamoDB table for storing world objects
        /// </summary>
        public string WorldObjectsTableName { get; set; } = "reapers-passing-world-objects";

        /// <summary>
        /// Name of the DynamoDB table for zone registry
        /// </summary>
        public string ZoneRegistryTableName { get; set; } = "reapers-passing-zone-registry";

        /// <summary>
        /// AWS region for DynamoDB (optional, uses default if not specified)
        /// </summary>
        public string? Region { get; set; }

        /// <summary>
        /// Batch save interval in seconds
        /// </summary>
        public int BatchSaveIntervalSeconds { get; set; } = 5;

        /// <summary>
        /// Maximum number of operations to process in a single batch
        /// </summary>
        public int MaxBatchSize { get; set; } = 25;

        /// <summary>
        /// Enable detailed logging of DynamoDB requests as JSON
        /// </summary>
        public bool EnableRequestLogging { get; set; } = false;

        /// <summary>
        /// Enable detailed logging of DynamoDB responses as JSON
        /// </summary>
        public bool EnableResponseLogging { get; set; } = false;

        /// <summary>
        /// Log level for DynamoDB request/response logging (Debug, Information, Warning)
        /// </summary>
        public string RequestLogLevel { get; set; } = "Debug";
    }
}
