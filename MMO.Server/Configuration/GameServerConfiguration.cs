namespace MMO.Server.Configuration
{
    /// <summary>
    /// Configuration settings for the game server
    /// </summary>
    public class GameServerConfiguration
    {
        public const string SectionName = "GameServer";

        /// <summary>
        /// Unique identifier for this zone/server instance
        /// </summary>
        public string ZoneId { get; set; } = "zone-1";

        /// <summary>
        /// Port number for the game server to listen on
        /// </summary>
        public int ServerPort { get; set; } = 7777;

        /// <summary>
        /// Heartbeat interval in seconds for zone registry updates
        /// </summary>
        public int HeartbeatIntervalSeconds { get; set; } = 30;

        /// <summary>
        /// Game loop update rate in Hz
        /// </summary>
        public float GameLoopUpdateRate { get; set; } = 20.0f;

        /// <summary>
        /// Maximum number of players allowed in this zone
        /// </summary>
        public int MaxPlayers { get; set; } = 100;

        /// <summary>
        /// Server region identifier
        /// </summary>
        public string Region { get; set; } = "us-east-1";

        /// <summary>
        /// Whether to enable debug logging for game systems
        /// </summary>
        public bool EnableDebugLogging { get; set; } = false;
    }
}
