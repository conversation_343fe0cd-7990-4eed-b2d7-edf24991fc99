# Use the official .NET runtime as a parent image
FROM mcr.microsoft.com/dotnet/runtime:8.0 AS base
WORKDIR /app

# Expose the game server port
EXPOSE 7777/tcp
EXPOSE 7777/udp

# Use the SDK image to build the application
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# Copy the project files
COPY ["MMO.Server/MMO.Server.csproj", "MMO.Server/"]
COPY ["MMO.Shared/MMO.Shared.csproj", "MMO.Shared/"]

# Restore dependencies
RUN dotnet restore "MMO.Server/MMO.Server.csproj"

# Copy the source code
COPY . .

# Build the application
WORKDIR "/src/MMO.Server"
RUN dotnet build "MMO.Server.csproj" -c Release -o /app/build

# Publish the application
FROM build AS publish
RUN dotnet publish "MMO.Server.csproj" -c Release -o /app/publish /p:UseAppHost=false

# Final stage/image
FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .

# Set environment variables
ENV ASPNETCORE_ENVIRONMENT=Production
ENV GameServer__ZoneId=zone-1
ENV GameServer__ServerPort=7777
ENV DynamoDb__PlayersTableName=reapers-passing-players
ENV DynamoDb__WorldObjectsTableName=reapers-passing-world-objects
ENV DynamoDb__ZoneRegistryTableName=reapers-passing-zone-registry

# Create a non-root user
RUN adduser --disabled-password --gecos '' appuser && chown -R appuser /app
USER appuser

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

ENTRYPOINT ["dotnet", "MMO.Server.dll"]
