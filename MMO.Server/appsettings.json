{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "DynamoDb": {"PlayersTableName": "reapers-passing-players", "WorldObjectsTableName": "reapers-passing-world-objects", "ZoneRegistryTableName": "reapers-passing-zone-registry", "BatchSaveIntervalSeconds": 5, "MaxBatchSize": 25, "EnableRequestLogging": false, "EnableResponseLogging": false, "RequestLogLevel": "Debug"}, "GameServer": {"ZoneId": "zone-1", "ServerPort": 7777, "HeartbeatIntervalSeconds": 30, "GameLoopUpdateRate": 20.0, "MaxPlayers": 100, "Region": "us-east-1", "EnableDebugLogging": false}}