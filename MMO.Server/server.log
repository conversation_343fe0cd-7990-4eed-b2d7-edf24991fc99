Building...
info: MMO.Server.Program[0]
      Starting Reaper's Passing Game Server...
info: MMO.Server.Services.DynamoDbClientFactory[0]
      Using DynamoDB region: us-east-1
info: MMO.Server.Services.DynamoDbClientFactory[0]
      Using DynamoDB service URL: http://localhost:8000
info: MMO.Server.Services.DynamoDbClientFactory[0]
      Creating local DynamoDB client with faux credentials
info: MMO.Server.Services.DynamoDbClientFactory[0]
      Using DynamoDB region: us-east-1
info: MMO.Server.Services.DynamoDbClientFactory[0]
      Using DynamoDB service URL: http://localhost:8000
info: MMO.Server.Services.DynamoDbClientFactory[0]
      Creating local DynamoDB client with faux credentials
info: Microsoft.Hosting.Lifetime[14]
      Now listening on: http://localhost:7778
info: MMO.Server.Services.GameServerService[0]
      Game server starting for zone: zone-debug-1 on port: 7778
info: MMO.Server.Services.GameServerService[0]
      Initializing game systems...
info: MMO.Server.Services.DynamoDbPersistenceService[0]
      Initializing DynamoDB persistence service...
info: Microsoft.Hosting.Lifetime[0]
      Application started. Press Ctrl+C to shut down.
info: Microsoft.Hosting.Lifetime[0]
      Hosting environment: Development
info: Microsoft.Hosting.Lifetime[0]
      Content root path: /Users/<USER>/Documents/projects/ReapersPassing/MMO.Server
info: MMO.Server.Services.DynamoDbPersistenceService[0]
      DynamoDB persistence service initialized successfully
info: MMO.Server.Managers.PlayerManager[0]
      Initializing player manager...
info: MMO.Server.Managers.PlayerManager[0]
      Player manager initialized successfully
info: MMO.Server.Systems.CombatSystem[0]
      Combat system initialized
info: MMO.Server.Systems.CyberneticsSystem[0]
      Cybernetics system initialized
info: MMO.Server.Systems.CraftingSystem[0]
      Crafting system initialized with 1 schematics
info: MMO.Server.Systems.EconomySystem[0]
      Economy system initialized with 0 active listings
info: MMO.Server.Systems.SocialSystem[0]
      Social system initialized with 0 guilds and 0 houses
info: MMO.Server.Services.GameServerService[0]
      All game systems initialized successfully
info: MMO.Server.Services.GameServerService[0]
      Registering with zone registry...
dbug: MMO.Server.Services.ZoneRegistryService[0]
      DynamoDB PutItem Request:
      {
        "item": {
          "zone_id": {
            "S": "zone-debug-1"
          },
          "zone_name": {
            "S": "Zone zone-debug-1"
          },
          "server_address": {
            "S": "localhost"
          },
          "server_port": {
            "N": "7778"
          },
          "status": {
            "S": "Online"
          },
          "max_players": {
            "N": "10"
          },
          "current_players": {
            "N": "0"
          },
          "region": {
            "S": "us-east-1"
          },
          "last_heartbeat": {
            "N": "1750172854"
          }
        },
        "tableName": "reapers-passing-zone-registry"
      }
dbug: MMO.Server.Services.ZoneRegistryService[0]
      DynamoDB RegisterZone Items for table reapers-passing-zone-registry:
      {
        "zone_id": {
          "S": "zone-debug-1"
        },
        "zone_name": {
          "S": "Zone zone-debug-1"
        },
        "server_address": {
          "S": "localhost"
        },
        "server_port": {
          "N": "7778"
        },
        "status": {
          "S": "Online"
        },
        "max_players": {
          "N": "10"
        },
        "current_players": {
          "N": "0"
        },
        "region": {
          "S": "us-east-1"
        },
        "last_heartbeat": {
          "N": "1750172854"
        }
      }
dbug: MMO.Server.Services.ZoneRegistryService[0]
      DynamoDB PutItem Response:
      {
        "responseMetadata": {
          "requestId": "ef06b39c-86ab-4aa6-a0ca-524f9c702e0e",
          "metadata": {},
          "checksumAlgorithm": 0,
          "checksumValidationStatus": 0
        },
        "contentLength": 2,
        "httpStatusCode": 200
      }
info: MMO.Server.Services.ZoneRegistryService[0]
      Successfully registered zone zone-debug-1 at localhost:7778
info: MMO.Server.Services.GameServerService[0]
      Successfully registered with zone registry
info: MMO.Server.Services.GameServerService[0]
      Game server started successfully for zone: zone-debug-1
dbug: MMO.Server.Services.ZoneRegistryService[0]
      DynamoDB UpdateItem Request:
      {
        "expressionAttributeValues": {
          ":heartbeat": {
            "N": "1750172854"
          },
          ":players": {
            "N": "0"
          }
        },
        "key": {
          "zone_id": {
            "S": "zone-debug-1"
          }
        },
        "tableName": "reapers-passing-zone-registry",
        "updateExpression": "SET last_heartbeat = :heartbeat, current_players = :players"
      }
dbug: MMO.Server.Services.ZoneRegistryService[0]
      DynamoDB UpdateHeartbeat Key for table reapers-passing-zone-registry:
      {
        "zone_id": {
          "S": "zone-debug-1"
        }
      }
dbug: MMO.Server.Services.ZoneRegistryService[0]
      DynamoDB UpdateHeartbeat Update Expression for table reapers-passing-zone-registry:
      {
        "updateExpression": "SET last_heartbeat = :heartbeat, current_players = :players",
        "expressionAttributeValues": {
          ":heartbeat": {
            "N": "1750172854"
          },
          ":players": {
            "N": "0"
          }
        }
      }
dbug: MMO.Server.Services.ZoneRegistryService[0]
      DynamoDB UpdateItem Response:
      {
        "responseMetadata": {
          "requestId": "40505605-bbf7-4892-a0de-68acd67008e2",
          "metadata": {},
          "checksumAlgorithm": 0,
          "checksumValidationStatus": 0
        },
        "contentLength": 2,
        "httpStatusCode": 200
      }
dbug: MMO.Server.Services.ZoneRegistryService[0]
      Updated heartbeat for zone zone-debug-1 with 0 players
dbug: MMO.Server.Services.GameServerService[0]
      Heartbeat sent for zone zone-debug-1 with 0 players
info: Microsoft.AspNetCore.Hosting.Diagnostics[1]
      Request starting HTTP/1.1 GET http://localhost:7778/health - - -
info: Microsoft.AspNetCore.Routing.EndpointMiddleware[0]
      Executing endpoint 'HTTP: GET /health'
info: Microsoft.AspNetCore.Routing.EndpointMiddleware[1]
      Executed endpoint 'HTTP: GET /health'
info: Microsoft.AspNetCore.Hosting.Diagnostics[2]
      Request finished HTTP/1.1 GET http://localhost:7778/health - 200 - - 8.9949ms
