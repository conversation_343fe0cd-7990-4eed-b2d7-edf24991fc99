using Amazon;
using Amazon.DynamoDBv2;
using Amazon.Runtime;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MMO.Server.Configuration;

namespace MMO.Server.Services
{
    /// <summary>
    /// Factory for creating DynamoDB clients with proper configuration and credential management
    /// </summary>
    public class DynamoDbClientFactory : IDynamoDbClientFactory
    {
        private readonly ILogger<DynamoDbClientFactory> _logger;
        private readonly DynamoDbConfiguration _config;

        public DynamoDbClientFactory(ILogger<DynamoDbClientFactory> logger, IOptions<DynamoDbConfiguration> options)
        {
            _logger = logger;
            _config = options.Value;
        }

        /// <summary>
        /// Creates a configured DynamoDB client instance
        /// </summary>
        /// <returns>Configured AmazonDynamoDBClient</returns>
        public IAmazonDynamoDB CreateClient()
        {
            var dynamoConfig = new AmazonDynamoDBConfig();

            // Configure region
            if (!string.IsNullOrEmpty(_config.Region))
            {
                if (_config.Region.Equals("USEast1", StringComparison.OrdinalIgnoreCase))
                {
                    dynamoConfig.RegionEndpoint = RegionEndpoint.USEast1;
                    _logger.LogInformation("Using DynamoDB region: {Region}", RegionEndpoint.USEast1.SystemName);
                }
                else
                {
                    var region = RegionEndpoint.GetBySystemName(_config.Region);
                    dynamoConfig.RegionEndpoint = region;
                    _logger.LogInformation("Using DynamoDB region: {Region}", region.SystemName);
                }
            }
            else
            {
                // Default to us-east-1 if no region specified
                dynamoConfig.RegionEndpoint = RegionEndpoint.USEast1;
                _logger.LogInformation("Using default DynamoDB region: {Region}", RegionEndpoint.USEast1.SystemName);
            }

            // Configure service URL for local development
            if (!string.IsNullOrEmpty(_config.ServiceURL))
            {
                dynamoConfig.ServiceURL = _config.ServiceURL;
                _logger.LogInformation("Using DynamoDB service URL: {ServiceURL}", _config.ServiceURL);
            }

            // Determine credential strategy
            if (_config.UseFauxCredentials || !string.IsNullOrEmpty(_config.ServiceURL))
            {
                return CreateLocalClient(dynamoConfig);
            }
            else if (!string.IsNullOrEmpty(_config.AccessKeyId) && !string.IsNullOrEmpty(_config.SecretAccessKey))
            {
                return CreateClientWithCredentials(dynamoConfig);
            }

            // For AWS DynamoDB, use default credential chain
            _logger.LogInformation("Creating AWS DynamoDB client with default credential chain");
            return new AmazonDynamoDBClient(dynamoConfig);
        }

        /// <summary>
        /// Creates a DynamoDB client for local development with faux credentials
        /// </summary>
        /// <param name="config">DynamoDB configuration</param>
        /// <returns>Configured client for local DynamoDB</returns>
        private IAmazonDynamoDB CreateLocalClient(AmazonDynamoDBConfig config)
        {
            _logger.LogInformation("Creating local DynamoDB client with faux credentials");

            // Create basic AWS credentials for local DynamoDB
            // These are faux credentials that DynamoDB Local accepts
            var credentials = new BasicAWSCredentials("fakeMyKeyId", "fakeSecretAccessKey");

            return new AmazonDynamoDBClient(credentials, config);
        }

        /// <summary>
        /// Creates a DynamoDB client with explicit credentials
        /// </summary>
        /// <param name="config">DynamoDB configuration</param>
        /// <returns>Configured client with explicit credentials</returns>
        private IAmazonDynamoDB CreateClientWithCredentials(AmazonDynamoDBConfig config)
        {
            _logger.LogInformation("Creating DynamoDB client with explicit credentials");

            var credentials = new BasicAWSCredentials(_config.AccessKeyId!, _config.SecretAccessKey!);

            return new AmazonDynamoDBClient(credentials, config);
        }
    }
}
