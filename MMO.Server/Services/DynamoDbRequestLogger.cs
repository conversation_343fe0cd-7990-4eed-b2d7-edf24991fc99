using Amazon.DynamoDBv2.Model;
using Microsoft.Extensions.Logging;
using MMO.Server.Configuration;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace MMO.Server.Services
{
    /// <summary>
    /// Helper class for logging DynamoDB requests and responses as JSON
    /// </summary>
    public static class DynamoDbRequestLogger
    {
        private static readonly JsonSerializerOptions JsonOptions = new()
        {
            WriteIndented = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
            Converters = { new AttributeValueJsonConverter() }
        };

        /// <summary>
        /// Logs a DynamoDB request as JSON if logging is enabled
        /// </summary>
        public static void LogRequest<T>(ILogger logger, DynamoDbConfiguration config, string operation, T request)
        {
            if (!config.EnableRequestLogging) return;

            try
            {
                var requestJson = JsonSerializer.Serialize(request, JsonOptions);
                var logLevel = ParseLogLevel(config.RequestLogLevel);
                
                logger.Log(logLevel, "DynamoDB {Operation} Request: {RequestJson}", 
                    operation, requestJson);
            }
            catch (Exception ex)
            {
                logger.LogWarning(ex, "Failed to serialize DynamoDB request for logging");
            }
        }

        /// <summary>
        /// Logs a DynamoDB response as JSON if logging is enabled
        /// </summary>
        public static void LogResponse<T>(ILogger logger, DynamoDbConfiguration config, string operation, T response)
        {
            if (!config.EnableResponseLogging) return;

            try
            {
                var responseJson = JsonSerializer.Serialize(response, JsonOptions);
                var logLevel = ParseLogLevel(config.RequestLogLevel);
                
                logger.Log(logLevel, "DynamoDB {Operation} Response: {ResponseJson}", 
                    operation, responseJson);
            }
            catch (Exception ex)
            {
                logger.LogWarning(ex, "Failed to serialize DynamoDB response for logging");
            }
        }

        /// <summary>
        /// Logs DynamoDB request items in a structured format
        /// </summary>
        public static void LogRequestItems(ILogger logger, DynamoDbConfiguration config, string operation, 
            Dictionary<string, AttributeValue> items, string? tableName = null)
        {
            if (!config.EnableRequestLogging) return;

            try
            {
                var itemsJson = JsonSerializer.Serialize(items, JsonOptions);
                var logLevel = ParseLogLevel(config.RequestLogLevel);
                
                var message = tableName != null 
                    ? "DynamoDB {Operation} Items for table {TableName}: {ItemsJson}"
                    : "DynamoDB {Operation} Items: {ItemsJson}";
                
                if (tableName != null)
                {
                    logger.Log(logLevel, message, operation, tableName, itemsJson);
                }
                else
                {
                    logger.Log(logLevel, message, operation, itemsJson);
                }
            }
            catch (Exception ex)
            {
                logger.LogWarning(ex, "Failed to serialize DynamoDB items for logging");
            }
        }

        /// <summary>
        /// Logs DynamoDB key items in a structured format
        /// </summary>
        public static void LogKeyItems(ILogger logger, DynamoDbConfiguration config, string operation, 
            Dictionary<string, AttributeValue> key, string? tableName = null)
        {
            if (!config.EnableRequestLogging) return;

            try
            {
                var keyJson = JsonSerializer.Serialize(key, JsonOptions);
                var logLevel = ParseLogLevel(config.RequestLogLevel);
                
                var message = tableName != null 
                    ? "DynamoDB {Operation} Key for table {TableName}: {KeyJson}"
                    : "DynamoDB {Operation} Key: {KeyJson}";
                
                if (tableName != null)
                {
                    logger.Log(logLevel, message, operation, tableName, keyJson);
                }
                else
                {
                    logger.Log(logLevel, message, operation, keyJson);
                }
            }
            catch (Exception ex)
            {
                logger.LogWarning(ex, "Failed to serialize DynamoDB key for logging");
            }
        }

        /// <summary>
        /// Logs DynamoDB update expressions and attribute values
        /// </summary>
        public static void LogUpdateExpression(ILogger logger, DynamoDbConfiguration config, string operation,
            string updateExpression, Dictionary<string, AttributeValue>? expressionAttributeValues = null,
            Dictionary<string, string>? expressionAttributeNames = null, string? tableName = null)
        {
            if (!config.EnableRequestLogging) return;

            try
            {
                var updateInfo = new
                {
                    UpdateExpression = updateExpression,
                    ExpressionAttributeValues = expressionAttributeValues,
                    ExpressionAttributeNames = expressionAttributeNames
                };

                var updateJson = JsonSerializer.Serialize(updateInfo, JsonOptions);
                var logLevel = ParseLogLevel(config.RequestLogLevel);
                
                var message = tableName != null 
                    ? "DynamoDB {Operation} Update Expression for table {TableName}: {UpdateJson}"
                    : "DynamoDB {Operation} Update Expression: {UpdateJson}";
                
                if (tableName != null)
                {
                    logger.Log(logLevel, message, operation, tableName, updateJson);
                }
                else
                {
                    logger.Log(logLevel, message, operation, updateJson);
                }
            }
            catch (Exception ex)
            {
                logger.LogWarning(ex, "Failed to serialize DynamoDB update expression for logging");
            }
        }

        private static LogLevel ParseLogLevel(string logLevel)
        {
            return logLevel.ToLowerInvariant() switch
            {
                "debug" => LogLevel.Debug,
                "information" => LogLevel.Information,
                "warning" => LogLevel.Warning,
                "error" => LogLevel.Error,
                _ => LogLevel.Debug
            };
        }
    }

    /// <summary>
    /// Custom JSON converter for DynamoDB AttributeValue objects
    /// </summary>
    public class AttributeValueJsonConverter : JsonConverter<AttributeValue>
    {
        public override AttributeValue Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            throw new NotImplementedException("Reading AttributeValue from JSON is not implemented");
        }

        public override void Write(Utf8JsonWriter writer, AttributeValue value, JsonSerializerOptions options)
        {
            writer.WriteStartObject();

            if (value.S != null)
            {
                writer.WriteString("S", value.S);
            }
            else if (value.N != null)
            {
                writer.WriteString("N", value.N);
            }
            else if (value.B != null)
            {
                writer.WriteBase64String("B", value.B.ToArray());
            }
            else if (value.BOOL.HasValue)
            {
                writer.WriteBoolean("BOOL", value.BOOL.Value);
            }
            else if (value.NULL == true)
            {
                writer.WriteBoolean("NULL", true);
            }
            else if (value.SS?.Count > 0)
            {
                writer.WriteStartArray("SS");
                foreach (var item in value.SS)
                {
                    writer.WriteStringValue(item);
                }
                writer.WriteEndArray();
            }
            else if (value.NS?.Count > 0)
            {
                writer.WriteStartArray("NS");
                foreach (var item in value.NS)
                {
                    writer.WriteStringValue(item);
                }
                writer.WriteEndArray();
            }
            else if (value.BS?.Count > 0)
            {
                writer.WriteStartArray("BS");
                foreach (var item in value.BS)
                {
                    writer.WriteBase64StringValue(item.ToArray());
                }
                writer.WriteEndArray();
            }
            else if (value.M?.Count > 0)
            {
                writer.WriteStartObject("M");
                foreach (var kvp in value.M)
                {
                    writer.WritePropertyName(kvp.Key);
                    Write(writer, kvp.Value, options);
                }
                writer.WriteEndObject();
            }
            else if (value.L?.Count > 0)
            {
                writer.WriteStartArray("L");
                foreach (var item in value.L)
                {
                    Write(writer, item, options);
                }
                writer.WriteEndArray();
            }

            writer.WriteEndObject();
        }
    }
}
