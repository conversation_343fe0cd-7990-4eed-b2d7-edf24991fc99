using Amazon.DynamoDBv2;
using Amazon.DynamoDBv2.Model;
using Microsoft.Extensions.Logging;
using MMO.Shared.Models.Networking;

namespace MMO.Server.Services
{
    /// <summary>
    /// Implementation of zone registry service using DynamoDB
    /// </summary>
    public class ZoneRegistryService : IZoneRegistryService
    {
        private readonly ILogger<ZoneRegistryService> _logger;
        private readonly IAmazonDynamoDB _dynamoDbClient;
        private readonly string _zoneRegistryTableName;

        public ZoneRegistryService(ILogger<ZoneRegistryService> logger)
        {
            _logger = logger;
            _dynamoDbClient = new AmazonDynamoDBClient();
            _zoneRegistryTableName = Environment.GetEnvironmentVariable("ZONE_REGISTRY_TABLE_NAME") 
                ?? "reapers-passing-zone-registry";
        }

        public async Task RegisterZoneAsync(string zoneId, string serverAddress, int serverPort)
        {
            try
            {
                var request = new PutItemRequest
                {
                    TableName = _zoneRegistryTableName,
                    Item = new Dictionary<string, AttributeValue>
                    {
                        { "zone_id", new AttributeValue { S = zoneId } },
                        { "zone_name", new AttributeValue { S = $"Zone {zoneId}" } },
                        { "server_address", new AttributeValue { S = serverAddress } },
                        { "server_port", new AttributeValue { N = serverPort.ToString() } },
                        { "status", new AttributeValue { S = ServerStatus.Online.ToString() } },
                        { "max_players", new AttributeValue { N = "100" } },
                        { "current_players", new AttributeValue { N = "0" } },
                        { "region", new AttributeValue { S = Environment.GetEnvironmentVariable("AWS_REGION") ?? "us-east-1" } },
                        { "last_heartbeat", new AttributeValue { N = DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString() } }
                    }
                };

                await _dynamoDbClient.PutItemAsync(request);
                _logger.LogInformation("Successfully registered zone {ZoneId} at {Address}:{Port}", 
                    zoneId, serverAddress, serverPort);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to register zone {ZoneId}", zoneId);
                throw;
            }
        }

        public async Task UpdateHeartbeatAsync(string zoneId, int currentPlayerCount)
        {
            try
            {
                var request = new UpdateItemRequest
                {
                    TableName = _zoneRegistryTableName,
                    Key = new Dictionary<string, AttributeValue>
                    {
                        { "zone_id", new AttributeValue { S = zoneId } }
                    },
                    UpdateExpression = "SET last_heartbeat = :heartbeat, current_players = :players",
                    ExpressionAttributeValues = new Dictionary<string, AttributeValue>
                    {
                        { ":heartbeat", new AttributeValue { N = DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString() } },
                        { ":players", new AttributeValue { N = currentPlayerCount.ToString() } }
                    }
                };

                await _dynamoDbClient.UpdateItemAsync(request);
                _logger.LogDebug("Updated heartbeat for zone {ZoneId} with {PlayerCount} players", 
                    zoneId, currentPlayerCount);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to update heartbeat for zone {ZoneId}", zoneId);
                // Don't throw here as heartbeat failures shouldn't crash the server
            }
        }

        public async Task UnregisterZoneAsync(string zoneId)
        {
            try
            {
                var request = new UpdateItemRequest
                {
                    TableName = _zoneRegistryTableName,
                    Key = new Dictionary<string, AttributeValue>
                    {
                        { "zone_id", new AttributeValue { S = zoneId } }
                    },
                    UpdateExpression = "SET #status = :status",
                    ExpressionAttributeNames = new Dictionary<string, string>
                    {
                        { "#status", "status" }
                    },
                    ExpressionAttributeValues = new Dictionary<string, AttributeValue>
                    {
                        { ":status", new AttributeValue { S = ServerStatus.Offline.ToString() } }
                    }
                };

                await _dynamoDbClient.UpdateItemAsync(request);
                _logger.LogInformation("Successfully unregistered zone {ZoneId}", zoneId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to unregister zone {ZoneId}", zoneId);
                // Don't throw during shutdown
            }
        }
    }
}
