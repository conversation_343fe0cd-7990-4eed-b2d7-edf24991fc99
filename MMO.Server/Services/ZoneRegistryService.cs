using Amazon.DynamoDBv2;
using Amazon.DynamoDBv2.Model;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MMO.Shared.Models.Networking;
using MMO.Server.Configuration;

namespace MMO.Server.Services
{
    /// <summary>
    /// Implementation of zone registry service using DynamoDB
    /// </summary>
    public class ZoneRegistryService : IZoneRegistryService
    {
        private readonly ILogger<ZoneRegistryService> _logger;
        private readonly IAmazonDynamoDB _dynamoDbClient;
        private readonly DynamoDbConfiguration _config;
        private readonly GameServerConfiguration _gameConfig;

        public ZoneRegistryService(
            ILogger<ZoneRegistryService> logger,
            IOptions<DynamoDbConfiguration> dynamoOptions,
            IOptions<GameServerConfiguration> gameOptions,
            IDynamoDbClientFactory clientFactory)
        {
            _logger = logger;
            _dynamoDbClient = clientFactory.CreateClient();
            _config = dynamoOptions.Value;
            _gameConfig = gameOptions.Value;
        }

        public async Task RegisterZoneAsync(string zoneId, string serverAddress, int serverPort)
        {
            try
            {
                var request = new PutItemRequest
                {
                    TableName = _config.ZoneRegistryTableName,
                    Item = new Dictionary<string, AttributeValue>
                    {
                        { "zone_id", new AttributeValue { S = zoneId } },
                        { "zone_name", new AttributeValue { S = $"Zone {zoneId}" } },
                        { "server_address", new AttributeValue { S = serverAddress } },
                        { "server_port", new AttributeValue { N = serverPort.ToString() } },
                        { "status", new AttributeValue { S = ServerStatus.Online.ToString() } },
                        { "max_players", new AttributeValue { N = _gameConfig.MaxPlayers.ToString() } },
                        { "current_players", new AttributeValue { N = "0" } },
                        { "region", new AttributeValue { S = _gameConfig.Region } },
                        { "last_heartbeat", new AttributeValue { N = DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString() } }
                    }
                };

                // Log the request if enabled
                DynamoDbRequestLogger.LogRequest(_logger, _config, "PutItem", request);
                DynamoDbRequestLogger.LogRequestItems(_logger, _config, "RegisterZone", request.Item, request.TableName);

                var response = await _dynamoDbClient.PutItemAsync(request);

                // Log the response if enabled
                DynamoDbRequestLogger.LogResponse(_logger, _config, "PutItem", response);
                _logger.LogInformation("Successfully registered zone {ZoneId} at {Address}:{Port}", 
                    zoneId, serverAddress, serverPort);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to register zone {ZoneId}", zoneId);
                throw;
            }
        }

        public async Task UpdateHeartbeatAsync(string zoneId, int currentPlayerCount)
        {
            try
            {
                var request = new UpdateItemRequest
                {
                    TableName = _config.ZoneRegistryTableName,
                    Key = new Dictionary<string, AttributeValue>
                    {
                        { "zone_id", new AttributeValue { S = zoneId } }
                    },
                    UpdateExpression = "SET last_heartbeat = :heartbeat, current_players = :players",
                    ExpressionAttributeValues = new Dictionary<string, AttributeValue>
                    {
                        { ":heartbeat", new AttributeValue { N = DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString() } },
                        { ":players", new AttributeValue { N = currentPlayerCount.ToString() } }
                    }
                };

                // Log the request if enabled
                DynamoDbRequestLogger.LogRequest(_logger, _config, "UpdateItem", request);
                DynamoDbRequestLogger.LogKeyItems(_logger, _config, "UpdateHeartbeat", request.Key, request.TableName);
                DynamoDbRequestLogger.LogUpdateExpression(_logger, _config, "UpdateHeartbeat",
                    request.UpdateExpression, request.ExpressionAttributeValues, null, request.TableName);

                var response = await _dynamoDbClient.UpdateItemAsync(request);

                // Log the response if enabled
                DynamoDbRequestLogger.LogResponse(_logger, _config, "UpdateItem", response);

                _logger.LogDebug("Updated heartbeat for zone {ZoneId} with {PlayerCount} players",
                    zoneId, currentPlayerCount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to update heartbeat for zone {ZoneId}", zoneId);
                throw;
            }
        }

        public async Task UnregisterZoneAsync(string zoneId)
        {
            try
            {
                var request = new UpdateItemRequest
                {
                    TableName = _config.ZoneRegistryTableName,
                    Key = new Dictionary<string, AttributeValue>
                    {
                        { "zone_id", new AttributeValue { S = zoneId } }
                    },
                    UpdateExpression = "SET #status = :status",
                    ExpressionAttributeNames = new Dictionary<string, string>
                    {
                        { "#status", "status" }
                    },
                    ExpressionAttributeValues = new Dictionary<string, AttributeValue>
                    {
                        { ":status", new AttributeValue { S = ServerStatus.Offline.ToString() } }
                    }
                };

                // Log the request if enabled
                DynamoDbRequestLogger.LogRequest(_logger, _config, "UpdateItem", request);
                DynamoDbRequestLogger.LogKeyItems(_logger, _config, "UnregisterZone", request.Key, request.TableName);
                DynamoDbRequestLogger.LogUpdateExpression(_logger, _config, "UnregisterZone",
                    request.UpdateExpression, request.ExpressionAttributeValues, request.ExpressionAttributeNames, request.TableName);

                var response = await _dynamoDbClient.UpdateItemAsync(request);

                // Log the response if enabled
                DynamoDbRequestLogger.LogResponse(_logger, _config, "UpdateItem", response);

                _logger.LogInformation("Successfully unregistered zone {ZoneId}", zoneId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to unregister zone {ZoneId}", zoneId);
                // Don't throw during shutdown
            }
        }
    }
}
