namespace MMO.Server.Services
{
    /// <summary>
    /// Service for managing zone registration with the central registry
    /// </summary>
    public interface IZoneRegistryService
    {
        /// <summary>
        /// Registers this server as handling a specific zone
        /// </summary>
        Task RegisterZoneAsync(string zoneId, string serverAddress, int serverPort);
        
        /// <summary>
        /// Updates the heartbeat for this zone
        /// </summary>
        Task UpdateHeartbeatAsync(string zoneId, int currentPlayerCount);
        
        /// <summary>
        /// Unregisters this zone from the registry
        /// </summary>
        Task UnregisterZoneAsync(string zoneId);
    }
}
