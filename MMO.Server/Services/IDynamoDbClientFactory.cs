using Amazon.DynamoDBv2;

namespace MMO.Server.Services
{
    /// <summary>
    /// Factory interface for creating DynamoDB clients with proper configuration
    /// </summary>
    public interface IDynamoDbClientFactory
    {
        /// <summary>
        /// Creates a configured DynamoDB client instance
        /// </summary>
        /// <returns>Configured AmazonDynamoDBClient</returns>
        IAmazonDynamoDB CreateClient();
    }
}
