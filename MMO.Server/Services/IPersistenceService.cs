using MMO.Shared.Models.Characters;

namespace MMO.Server.Services
{
    /// <summary>
    /// Service for persisting game data to storage
    /// </summary>
    public interface IPersistenceService
    {
        /// <summary>
        /// Initializes the persistence service
        /// </summary>
        Task InitializeAsync();
        
        /// <summary>
        /// Shuts down the persistence service
        /// </summary>
        Task ShutdownAsync();
        
        /// <summary>
        /// Processes any pending persistence operations
        /// </summary>
        Task ProcessPendingOperationsAsync();
        
        /// <summary>
        /// Saves a player's data
        /// </summary>
        Task SavePlayerAsync(Player player);
        
        /// <summary>
        /// Loads a player's data
        /// </summary>
        Task<Player?> LoadPlayerAsync(string playerId);
        
        /// <summary>
        /// Saves world object data
        /// </summary>
        Task SaveWorldObjectAsync(string objectId, object objectData);
        
        /// <summary>
        /// Loads world object data
        /// </summary>
        Task<T?> LoadWorldObjectAsync<T>(string objectId) where T : class;
        
        /// <summary>
        /// Queues a save operation for batch processing
        /// </summary>
        void QueueSaveOperation(string key, object data);
        
        /// <summary>
        /// Forces immediate save of all queued operations
        /// </summary>
        Task FlushQueuedOperationsAsync();
    }
}
