using Amazon.DynamoDBv2;
using Amazon.DynamoDBv2.Model;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MMO.Shared.Models.Characters;
using MMO.Server.Configuration;
using System.Collections.Concurrent;
using System.Text.Json;
using Amazon;

namespace MMO.Server.Services
{
    /// <summary>
    /// DynamoDB implementation of the persistence service
    /// </summary>
    public class DynamoDbPersistenceService : IPersistenceService
    {
        private readonly ILogger<DynamoDbPersistenceService> _logger;
        private readonly IAmazonDynamoDB _dynamoDbClient;
        private readonly DynamoDbConfiguration _config;
        
        private readonly ConcurrentQueue<(string Key, object Data)> _saveQueue;
        private readonly Timer _batchSaveTimer;
        private readonly SemaphoreSlim _saveSemaphore;

        public DynamoDbPersistenceService(ILogger<DynamoDbPersistenceService> logger, IOptions<DynamoDbConfiguration> options)
        {
            _logger = logger;
            _config = options.Value;
            
            // Create DynamoDB client with custom configuration
            RegionEndpoint region = RegionEndpoint.GetBySystemName(_config.Region ?? "us-east-1"); 
            _logger.LogInformation("Using DynamoDB region: {Region}", region.SystemName);
            _logger.LogInformation("Using DynamoDB service URL: {ServiceURL}", _config.ServiceURL);
            
            var dynamoConfig = new AmazonDynamoDBConfig ();
            
            //if (!string.IsNullOrEmpty(_config.Region))
            //    dynamoConfig.RegionEndpoint = RegionEndpoint.GetBySystemName(_config.Region);
            
            if (_config.Region == "USEast1")
                dynamoConfig.RegionEndpoint = RegionEndpoint.USEast1;
            
            if (!string.IsNullOrEmpty(_config.ServiceURL))
                dynamoConfig.ServiceURL = _config.ServiceURL;
            
            _dynamoDbClient = new AmazonDynamoDBClient(dynamoConfig);
            
            _saveQueue = new ConcurrentQueue<(string, object)>();
            _saveSemaphore = new SemaphoreSlim(1, 1);
            
            // Process queued saves using configured interval
            _batchSaveTimer = new Timer(ProcessQueuedSaves, null, Timeout.Infinite, Timeout.Infinite);
        }

        public async Task InitializeAsync()
        {
            _logger.LogInformation("Initializing DynamoDB persistence service...");
            
            try
            {
                // Test connectivity by describing the tables
                await _dynamoDbClient.DescribeTableAsync(_config.PlayersTableName);
                await _dynamoDbClient.DescribeTableAsync(_config.WorldObjectsTableName);
                
                // Start the batch save timer using configured interval
                var interval = TimeSpan.FromSeconds(_config.BatchSaveIntervalSeconds);
                _batchSaveTimer.Change(interval, interval);
                
                _logger.LogInformation("DynamoDB persistence service initialized successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to initialize DynamoDB persistence service");
                throw;
            }
        }

        public async Task ShutdownAsync()
        {
            _logger.LogInformation("Shutting down persistence service...");
            
            // Stop the timer
            _batchSaveTimer?.Change(Timeout.Infinite, Timeout.Infinite);
            
            // Flush any remaining operations
            await FlushQueuedOperationsAsync();
            
            _logger.LogInformation("Persistence service shutdown completed");
        }

        public async Task ProcessPendingOperationsAsync()
        {
            if (_saveQueue.IsEmpty) return;

            await ProcessQueuedSavesAsync();
        }

        public async Task SavePlayerAsync(Player player)
        {
            try
            {
                var playerJson = JsonSerializer.Serialize(player);

                var request = new PutItemRequest
                {
                    TableName = _config.PlayersTableName,
                    Item = new Dictionary<string, AttributeValue>
                    {
                        { "player_id", new AttributeValue { S = player.PlayerId } },
                        { "character_name", new AttributeValue { S = player.CharacterName } },
                        { "user_id", new AttributeValue { S = player.UserId } },
                        { "level", new AttributeValue { N = player.Level.ToString() } },
                        { "experience_points", new AttributeValue { N = player.ExperiencePoints.ToString() } },
                        { "current_zone_id", new AttributeValue { S = player.CurrentZoneId } },
                        { "last_known_zone_id", new AttributeValue { S = player.LastKnownZoneId } },
                        { "position_x", new AttributeValue { N = player.Position.X.ToString() } },
                        { "position_y", new AttributeValue { N = player.Position.Y.ToString() } },
                        { "position_z", new AttributeValue { N = player.Position.Z.ToString() } },
                        { "current_health", new AttributeValue { N = player.CurrentHealth.ToString() } },
                        { "max_health", new AttributeValue { N = player.MaxHealth.ToString() } },
                        { "is_alive", new AttributeValue { BOOL = player.IsAlive } },
                        { "credits", new AttributeValue { N = player.Credits.ToString() } },
                        { "last_login_at", new AttributeValue { S = player.LastLoginAt.ToString("O") } },
                        { "player_data", new AttributeValue { S = playerJson } }
                    }
                };

                // Log the request if enabled
                DynamoDbRequestLogger.LogRequest(_logger, _config, "PutItem", request);
                DynamoDbRequestLogger.LogRequestItems(_logger, _config, "SavePlayer", request.Item, request.TableName);

                var response = await _dynamoDbClient.PutItemAsync(request);

                // Log the response if enabled
                DynamoDbRequestLogger.LogResponse(_logger, _config, "PutItem", response);

                _logger.LogDebug("Saved player data for {PlayerId}", player.PlayerId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to save player {PlayerId}", player.PlayerId);
                throw;
            }
        }

        public async Task<Player?> LoadPlayerAsync(string playerId)
        {
            try
            {
                var request = new GetItemRequest
                {
                    TableName = _config.PlayersTableName,
                    Key = new Dictionary<string, AttributeValue>
                    {
                        { "player_id", new AttributeValue { S = playerId } }
                    }
                };

                // Log the request if enabled
                DynamoDbRequestLogger.LogRequest(_logger, _config, "GetItem", request);
                DynamoDbRequestLogger.LogKeyItems(_logger, _config, "LoadPlayer", request.Key, request.TableName);

                var response = await _dynamoDbClient.GetItemAsync(request);

                // Log the response if enabled
                DynamoDbRequestLogger.LogResponse(_logger, _config, "GetItem", response);

                if (response.Item.Count == 0)
                {
                    _logger.LogDebug("Player {PlayerId} not found", playerId);
                    return null;
                }

                if (response.Item.TryGetValue("player_data", out var playerDataAttribute))
                {
                    var player = JsonSerializer.Deserialize<Player>(playerDataAttribute.S);
                    _logger.LogDebug("Loaded player data for {PlayerId}", playerId);
                    return player;
                }

                _logger.LogWarning("Player {PlayerId} found but no player_data field", playerId);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to load player {PlayerId}", playerId);
                throw;
            }
        }

        public async Task SaveWorldObjectAsync(string objectId, object objectData)
        {
            try
            {
                var objectJson = JsonSerializer.Serialize(objectData);

                var request = new PutItemRequest
                {
                    TableName = _config.WorldObjectsTableName,
                    Item = new Dictionary<string, AttributeValue>
                    {
                        { "object_id", new AttributeValue { S = objectId } },
                        { "object_data", new AttributeValue { S = objectJson } },
                        { "last_updated", new AttributeValue { S = DateTime.UtcNow.ToString("O") } }
                    }
                };

                // Log the request if enabled
                DynamoDbRequestLogger.LogRequest(_logger, _config, "PutItem", request);
                DynamoDbRequestLogger.LogRequestItems(_logger, _config, "SaveWorldObject", request.Item, request.TableName);

                var response = await _dynamoDbClient.PutItemAsync(request);

                // Log the response if enabled
                DynamoDbRequestLogger.LogResponse(_logger, _config, "PutItem", response);

                _logger.LogDebug("Saved world object {ObjectId}", objectId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to save world object {ObjectId}", objectId);
                throw;
            }
        }

        public async Task<T?> LoadWorldObjectAsync<T>(string objectId) where T : class
        {
            try
            {
                var request = new GetItemRequest
                {
                    TableName = _config.WorldObjectsTableName,
                    Key = new Dictionary<string, AttributeValue>
                    {
                        { "object_id", new AttributeValue { S = objectId } }
                    }
                };

                // Log the request if enabled
                DynamoDbRequestLogger.LogRequest(_logger, _config, "GetItem", request);
                DynamoDbRequestLogger.LogKeyItems(_logger, _config, "LoadWorldObject", request.Key, request.TableName);

                var response = await _dynamoDbClient.GetItemAsync(request);

                // Log the response if enabled
                DynamoDbRequestLogger.LogResponse(_logger, _config, "GetItem", response);

                if (response.Item.Count == 0)
                {
                    return null;
                }

                if (response.Item.TryGetValue("object_data", out var objectDataAttribute))
                {
                    return JsonSerializer.Deserialize<T>(objectDataAttribute.S);
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to load world object {ObjectId}", objectId);
                throw;
            }
        }

        public void QueueSaveOperation(string key, object data)
        {
            _saveQueue.Enqueue((key, data));
        }

        public async Task FlushQueuedOperationsAsync()
        {
            await ProcessQueuedSavesAsync();
        }

        private async void ProcessQueuedSaves(object? state)
        {
            await ProcessQueuedSavesAsync();
        }

        private async Task ProcessQueuedSavesAsync()
        {
            if (_saveQueue.IsEmpty) return;
            
            await _saveSemaphore.WaitAsync();
            
            try
            {
                var operations = new List<(string Key, object Data)>();
                
                // Dequeue up to configured max batch size
                while (operations.Count < _config.MaxBatchSize && _saveQueue.TryDequeue(out var operation))
                {
                    operations.Add(operation);
                }
                
                if (operations.Count == 0) return;
                
                _logger.LogDebug("Processing {Count} queued save operations", operations.Count);
                
                // Process operations in parallel (but limited)
                var tasks = operations.Select(async op =>
                {
                    try
                    {
                        if (op.Data is Player player)
                        {
                            await SavePlayerAsync(player);
                        }
                        else
                        {
                            await SaveWorldObjectAsync(op.Key, op.Data);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Failed to process queued save for {Key}", op.Key);
                    }
                });
                
                await Task.WhenAll(tasks);
                _logger.LogDebug("Completed processing {Count} queued save operations", operations.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing queued saves");
            }
            finally
            {
                _saveSemaphore.Release();
            }
        }

        public void Dispose()
        {
            _batchSaveTimer?.Dispose();
            _saveSemaphore?.Dispose();
        }
    }
}
