using Amazon.DynamoDBv2;
using Amazon.DynamoDBv2.Model;
using Microsoft.Extensions.Logging;
using MMO.Shared.Models.Characters;
using System.Collections.Concurrent;
using System.Text.Json;

namespace MMO.Server.Services
{
    /// <summary>
    /// DynamoDB implementation of the persistence service
    /// </summary>
    public class DynamoDbPersistenceService : IPersistenceService
    {
        private readonly ILogger<DynamoDbPersistenceService> _logger;
        private readonly IAmazonDynamoDB _dynamoDbClient;
        private readonly string _playersTableName;
        private readonly string _worldObjectsTableName;
        
        private readonly ConcurrentQueue<(string Key, object Data)> _saveQueue;
        private readonly Timer _batchSaveTimer;
        private readonly SemaphoreSlim _saveSemaphore;

        public DynamoDbPersistenceService(ILogger<DynamoDbPersistenceService> logger)
        {
            _logger = logger;
            _dynamoDbClient = new AmazonDynamoDBClient();
            _playersTableName = Environment.GetEnvironmentVariable("PLAYERS_TABLE_NAME") 
                ?? "reapers-passing-players";
            _worldObjectsTableName = Environment.GetEnvironmentVariable("WORLD_OBJECTS_TABLE_NAME") 
                ?? "reapers-passing-world-objects";
            
            _saveQueue = new ConcurrentQueue<(string, object)>();
            _saveSemaphore = new SemaphoreSlim(1, 1);
            
            // Process queued saves every 5 seconds
            _batchSaveTimer = new Timer(ProcessQueuedSaves, null, Timeout.Infinite, Timeout.Infinite);
        }

        public async Task InitializeAsync()
        {
            _logger.LogInformation("Initializing DynamoDB persistence service...");
            
            try
            {
                // Test connectivity by describing the tables
                await _dynamoDbClient.DescribeTableAsync(_playersTableName);
                await _dynamoDbClient.DescribeTableAsync(_worldObjectsTableName);
                
                // Start the batch save timer
                _batchSaveTimer.Change(TimeSpan.FromSeconds(5), TimeSpan.FromSeconds(5));
                
                _logger.LogInformation("DynamoDB persistence service initialized successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to initialize DynamoDB persistence service");
                throw;
            }
        }

        public async Task ShutdownAsync()
        {
            _logger.LogInformation("Shutting down persistence service...");
            
            // Stop the timer
            _batchSaveTimer?.Change(Timeout.Infinite, Timeout.Infinite);
            
            // Flush any remaining operations
            await FlushQueuedOperationsAsync();
            
            _logger.LogInformation("Persistence service shutdown completed");
        }

        public async Task ProcessPendingOperationsAsync()
        {
            if (_saveQueue.IsEmpty) return;

            await ProcessQueuedSavesAsync();
        }

        public async Task SavePlayerAsync(Player player)
        {
            try
            {
                var playerJson = JsonSerializer.Serialize(player);
                
                var request = new PutItemRequest
                {
                    TableName = _playersTableName,
                    Item = new Dictionary<string, AttributeValue>
                    {
                        { "player_id", new AttributeValue { S = player.PlayerId } },
                        { "character_name", new AttributeValue { S = player.CharacterName } },
                        { "user_id", new AttributeValue { S = player.UserId } },
                        { "level", new AttributeValue { N = player.Level.ToString() } },
                        { "experience_points", new AttributeValue { N = player.ExperiencePoints.ToString() } },
                        { "current_zone_id", new AttributeValue { S = player.CurrentZoneId } },
                        { "last_known_zone_id", new AttributeValue { S = player.LastKnownZoneId } },
                        { "position_x", new AttributeValue { N = player.Position.X.ToString() } },
                        { "position_y", new AttributeValue { N = player.Position.Y.ToString() } },
                        { "position_z", new AttributeValue { N = player.Position.Z.ToString() } },
                        { "current_health", new AttributeValue { N = player.CurrentHealth.ToString() } },
                        { "max_health", new AttributeValue { N = player.MaxHealth.ToString() } },
                        { "is_alive", new AttributeValue { BOOL = player.IsAlive } },
                        { "credits", new AttributeValue { N = player.Credits.ToString() } },
                        { "last_login_at", new AttributeValue { S = player.LastLoginAt.ToString("O") } },
                        { "player_data", new AttributeValue { S = playerJson } }
                    }
                };

                await _dynamoDbClient.PutItemAsync(request);
                _logger.LogDebug("Saved player data for {PlayerId}", player.PlayerId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to save player {PlayerId}", player.PlayerId);
                throw;
            }
        }

        public async Task<Player?> LoadPlayerAsync(string playerId)
        {
            try
            {
                var request = new GetItemRequest
                {
                    TableName = _playersTableName,
                    Key = new Dictionary<string, AttributeValue>
                    {
                        { "player_id", new AttributeValue { S = playerId } }
                    }
                };

                var response = await _dynamoDbClient.GetItemAsync(request);
                
                if (response.Item.Count == 0)
                {
                    _logger.LogDebug("Player {PlayerId} not found", playerId);
                    return null;
                }

                if (response.Item.TryGetValue("player_data", out var playerDataAttribute))
                {
                    var player = JsonSerializer.Deserialize<Player>(playerDataAttribute.S);
                    _logger.LogDebug("Loaded player data for {PlayerId}", playerId);
                    return player;
                }

                _logger.LogWarning("Player {PlayerId} found but no player_data field", playerId);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to load player {PlayerId}", playerId);
                throw;
            }
        }

        public async Task SaveWorldObjectAsync(string objectId, object objectData)
        {
            try
            {
                var objectJson = JsonSerializer.Serialize(objectData);
                
                var request = new PutItemRequest
                {
                    TableName = _worldObjectsTableName,
                    Item = new Dictionary<string, AttributeValue>
                    {
                        { "object_id", new AttributeValue { S = objectId } },
                        { "object_data", new AttributeValue { S = objectJson } },
                        { "last_updated", new AttributeValue { S = DateTime.UtcNow.ToString("O") } }
                    }
                };

                await _dynamoDbClient.PutItemAsync(request);
                _logger.LogDebug("Saved world object {ObjectId}", objectId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to save world object {ObjectId}", objectId);
                throw;
            }
        }

        public async Task<T?> LoadWorldObjectAsync<T>(string objectId) where T : class
        {
            try
            {
                var request = new GetItemRequest
                {
                    TableName = _worldObjectsTableName,
                    Key = new Dictionary<string, AttributeValue>
                    {
                        { "object_id", new AttributeValue { S = objectId } }
                    }
                };

                var response = await _dynamoDbClient.GetItemAsync(request);
                
                if (response.Item.Count == 0)
                {
                    return null;
                }

                if (response.Item.TryGetValue("object_data", out var objectDataAttribute))
                {
                    return JsonSerializer.Deserialize<T>(objectDataAttribute.S);
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to load world object {ObjectId}", objectId);
                throw;
            }
        }

        public void QueueSaveOperation(string key, object data)
        {
            _saveQueue.Enqueue((key, data));
        }

        public async Task FlushQueuedOperationsAsync()
        {
            await ProcessQueuedSavesAsync();
        }

        private async void ProcessQueuedSaves(object? state)
        {
            await ProcessQueuedSavesAsync();
        }

        private async Task ProcessQueuedSavesAsync()
        {
            if (_saveQueue.IsEmpty) return;
            
            await _saveSemaphore.WaitAsync();
            
            try
            {
                var operations = new List<(string Key, object Data)>();
                
                // Dequeue up to 25 operations (DynamoDB batch limit)
                while (operations.Count < 25 && _saveQueue.TryDequeue(out var operation))
                {
                    operations.Add(operation);
                }
                
                if (operations.Count == 0) return;
                
                _logger.LogDebug("Processing {Count} queued save operations", operations.Count);
                
                // Process operations in parallel (but limited)
                var tasks = operations.Select(async op =>
                {
                    try
                    {
                        if (op.Data is Player player)
                        {
                            await SavePlayerAsync(player);
                        }
                        else
                        {
                            await SaveWorldObjectAsync(op.Key, op.Data);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Failed to process queued save for {Key}", op.Key);
                    }
                });
                
                await Task.WhenAll(tasks);
                _logger.LogDebug("Completed processing {Count} queued save operations", operations.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing queued saves");
            }
            finally
            {
                _saveSemaphore.Release();
            }
        }

        public void Dispose()
        {
            _batchSaveTimer?.Dispose();
            _saveSemaphore?.Dispose();
        }
    }
}
