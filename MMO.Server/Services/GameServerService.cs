using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using MMO.Server.Managers;
using MMO.Server.Systems;
using MMO.Shared.Models.Common;

namespace MMO.Server.Services
{
    /// <summary>
    /// Main game server service that coordinates all game systems
    /// </summary>
    public class GameServerService : BackgroundService
    {
        private readonly ILogger<GameServerService> _logger;
        private readonly IZoneRegistryService _zoneRegistry;
        private readonly IPlayerManager _playerManager;
        private readonly IWorldObjectManager _worldObjectManager;
        private readonly ICombatSystem _combatSystem;
        private readonly ICyberneticsSystem _cyberneticsSystem;
        private readonly ICraftingSystem _craftingSystem;
        private readonly IEconomySystem _economySystem;
        private readonly ISocialSystem _socialSystem;
        private readonly IPersistenceService _persistenceService;

        private readonly string _zoneId;
        private readonly int _serverPort;
        private readonly Timer _heartbeatTimer;
        private readonly Timer _gameLoopTimer;

        public GameServerService(
            ILogger<GameServerService> logger,
            IZoneRegistryService zoneRegistry,
            IPlayerManager playerManager,
            IWorldObjectManager worldObjectManager,
            ICombatSystem combatSystem,
            ICyberneticsSystem cyberneticsSystem,
            ICraftingSystem craftingSystem,
            IEconomySystem economySystem,
            ISocialSystem socialSystem,
            IPersistenceService persistenceService)
        {
            _logger = logger;
            _zoneRegistry = zoneRegistry;
            _playerManager = playerManager;
            _worldObjectManager = worldObjectManager;
            _combatSystem = combatSystem;
            _cyberneticsSystem = cyberneticsSystem;
            _craftingSystem = craftingSystem;
            _economySystem = economySystem;
            _socialSystem = socialSystem;
            _persistenceService = persistenceService;

            // Get configuration from environment variables
            _zoneId = Environment.GetEnvironmentVariable("ZONE_ID") ?? "zone-1";
            _serverPort = int.TryParse(Environment.GetEnvironmentVariable("SERVER_PORT"), out var port) 
                ? port : GameConstants.Networking.DefaultGamePort;

            // Setup timers
            _heartbeatTimer = new Timer(SendHeartbeat, null, Timeout.Infinite, Timeout.Infinite);
            _gameLoopTimer = new Timer(GameLoop, null, Timeout.Infinite, Timeout.Infinite);
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("Game server starting for zone: {ZoneId} on port: {Port}", _zoneId, _serverPort);

            try
            {
                // Initialize all systems
                await InitializeSystems();

                // Register with zone registry
                await RegisterWithZoneRegistry();

                // Start heartbeat timer (every 30 seconds)
                _heartbeatTimer.Change(TimeSpan.Zero, TimeSpan.FromSeconds(GameConstants.Zones.HeartbeatInterval));

                // Start game loop timer (50Hz = 20ms intervals)
                var gameLoopInterval = TimeSpan.FromMilliseconds(1000.0 / GameConstants.Networking.NetworkUpdateRate);
                _gameLoopTimer.Change(TimeSpan.Zero, gameLoopInterval);

                _logger.LogInformation("Game server started successfully for zone: {ZoneId}", _zoneId);

                // Keep the service running
                await Task.Delay(Timeout.Infinite, stoppingToken);
            }
            catch (OperationCanceledException)
            {
                _logger.LogInformation("Game server shutdown requested");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Game server encountered an error");
                throw;
            }
            finally
            {
                await Shutdown();
            }
        }

        private async Task InitializeSystems()
        {
            _logger.LogInformation("Initializing game systems...");

            // Initialize persistence service
            await _persistenceService.InitializeAsync();

            // Initialize managers
            await _playerManager.InitializeAsync();
            await _worldObjectManager.InitializeAsync();

            // Initialize game systems
            await _combatSystem.InitializeAsync();
            await _cyberneticsSystem.InitializeAsync();
            await _craftingSystem.InitializeAsync();
            await _economySystem.InitializeAsync();
            await _socialSystem.InitializeAsync();

            _logger.LogInformation("All game systems initialized successfully");
        }

        private async Task RegisterWithZoneRegistry()
        {
            _logger.LogInformation("Registering with zone registry...");

            try
            {
                await _zoneRegistry.RegisterZoneAsync(_zoneId, "localhost", _serverPort);
                _logger.LogInformation("Successfully registered with zone registry");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to register with zone registry");
                throw;
            }
        }

        private async void SendHeartbeat(object? state)
        {
            try
            {
                var playerCount = _playerManager.GetPlayerCount();
                await _zoneRegistry.UpdateHeartbeatAsync(_zoneId, playerCount);
                
                _logger.LogDebug("Heartbeat sent for zone {ZoneId} with {PlayerCount} players", _zoneId, playerCount);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to send heartbeat");
            }
        }

        private async void GameLoop(object? state)
        {
            try
            {
                var deltaTime = 1.0f / GameConstants.Networking.NetworkUpdateRate;

                // Update all game systems
                await _combatSystem.UpdateAsync(deltaTime);
                await _cyberneticsSystem.UpdateAsync(deltaTime);
                await _craftingSystem.UpdateAsync(deltaTime);
                await _economySystem.UpdateAsync(deltaTime);
                await _socialSystem.UpdateAsync(deltaTime);

                // Update managers
                await _playerManager.UpdateAsync(deltaTime);
                await _worldObjectManager.UpdateAsync(deltaTime);

                // Process any pending persistence operations
                await _persistenceService.ProcessPendingOperationsAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in game loop");
            }
        }

        private async Task Shutdown()
        {
            _logger.LogInformation("Shutting down game server...");

            // Stop timers
            _heartbeatTimer?.Change(Timeout.Infinite, Timeout.Infinite);
            _gameLoopTimer?.Change(Timeout.Infinite, Timeout.Infinite);

            try
            {
                // Unregister from zone registry
                await _zoneRegistry.UnregisterZoneAsync(_zoneId);

                // Shutdown all systems
                await _persistenceService.ShutdownAsync();
                await _playerManager.ShutdownAsync();
                await _worldObjectManager.ShutdownAsync();

                _logger.LogInformation("Game server shutdown completed");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during shutdown");
            }
        }

        public override void Dispose()
        {
            _heartbeatTimer?.Dispose();
            _gameLoopTimer?.Dispose();
            base.Dispose();
        }
    }
}
