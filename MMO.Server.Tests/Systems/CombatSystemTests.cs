using Microsoft.Extensions.Logging;
using MMO.Server.Managers;
using MMO.Server.Systems;
using MMO.Shared.Models.Characters;
using MMO.Shared.Models.Common;
using MMO.Shared.Models.Items;
using Moq;
using Xunit;

namespace MMO.Server.Tests.Systems
{
    public class CombatSystemTests
    {
        private readonly Mock<ILogger<CombatSystem>> _mockLogger;
        private readonly Mock<IPlayerManager> _mockPlayerManager;
        private readonly CombatSystem _combatSystem;

        public CombatSystemTests()
        {
            _mockLogger = new Mock<ILogger<CombatSystem>>();
            _mockPlayerManager = new Mock<IPlayerManager>();
            _combatSystem = new CombatSystem(_mockLogger.Object, _mockPlayerManager.Object);
        }

        [Fact]
        public async Task InitializeAsync_ShouldComplete()
        {
            // Act
            await _combatSystem.InitializeAsync();
            
            // Assert - No exception should be thrown
            Assert.True(true);
        }

        [Fact]
        public void CalculateDamage_WithBallisticDamage_ShouldApplyCorrectModifiers()
        {
            // Arrange
            float baseDamage = 100f;
            var damageType = DamageType.Ballistic;
            var hitLocation = HitLocation.Torso;
            float armorRating = 50f;

            // Act
            float result = _combatSystem.CalculateDamage(baseDamage, damageType, hitLocation, armorRating);

            // Assert
            Assert.True(result > 0);
            Assert.True(result < baseDamage); // Should be reduced by armor
        }

        [Fact]
        public void CalculateDamage_WithEnergyDamage_ShouldApplyCorrectModifiers()
        {
            // Arrange
            float baseDamage = 100f;
            var damageType = DamageType.Energy;
            var hitLocation = HitLocation.Torso;
            float armorRating = 50f;

            // Act
            float result = _combatSystem.CalculateDamage(baseDamage, damageType, hitLocation, armorRating);

            // Assert
            Assert.True(result > 0);
            // Energy damage should be more effective against armor
        }

        [Fact]
        public void CalculateDamage_WithEMPDamage_ShouldApplyCorrectModifiers()
        {
            // Arrange
            float baseDamage = 100f;
            var damageType = DamageType.EMP;
            var hitLocation = HitLocation.Torso;
            float armorRating = 50f;

            // Act
            float result = _combatSystem.CalculateDamage(baseDamage, damageType, hitLocation, armorRating);

            // Assert
            Assert.True(result > 0);
        }

        [Fact]
        public void CalculateDamage_ShouldNeverReturnZero()
        {
            // Arrange
            float baseDamage = 1f;
            var damageType = DamageType.Ballistic;
            var hitLocation = HitLocation.Torso;
            float armorRating = 1000f; // Very high armor

            // Act
            float result = _combatSystem.CalculateDamage(baseDamage, damageType, hitLocation, armorRating);

            // Assert
            Assert.True(result >= 1f); // Should always deal at least 1 damage
        }

        [Fact]
        public async Task StartCombatAsync_ShouldSetPlayerInCombat()
        {
            // Arrange
            string playerId = "test-player";
            var player = new Player { PlayerId = playerId };
            _mockPlayerManager.Setup(x => x.GetPlayer(playerId)).Returns(player);

            // Act
            await _combatSystem.StartCombatAsync(playerId);

            // Assert
            Assert.True(_combatSystem.IsPlayerInCombat(playerId));
            Assert.Contains(player.ActiveStatusEffects, e => e.Type == StatusEffectType.InCombat);
        }

        [Fact]
        public async Task EndCombatAsync_ShouldRemovePlayerFromCombat()
        {
            // Arrange
            string playerId = "test-player";
            var player = new Player { PlayerId = playerId };
            _mockPlayerManager.Setup(x => x.GetPlayer(playerId)).Returns(player);

            // Start combat first
            await _combatSystem.StartCombatAsync(playerId);

            // Act
            await _combatSystem.EndCombatAsync(playerId);

            // Assert
            Assert.False(_combatSystem.IsPlayerInCombat(playerId));
            Assert.DoesNotContain(player.ActiveStatusEffects, e => e.Type == StatusEffectType.InCombat);
        }

        [Fact]
        public async Task ApplyDamageAsync_ShouldCallPlayerManager()
        {
            // Arrange
            string targetId = "target-player";
            float damage = 50f;
            var damageType = DamageType.Ballistic;
            string sourceId = "source-player";

            _mockPlayerManager.Setup(x => x.DamagePlayerAsync(targetId, damage, sourceId))
                .ReturnsAsync(false);

            // Act
            bool result = await _combatSystem.ApplyDamageAsync(targetId, damage, damageType, sourceId);

            // Assert
            _mockPlayerManager.Verify(x => x.DamagePlayerAsync(targetId, damage, sourceId), Times.Once);
            Assert.False(result);
        }

        [Fact]
        public async Task ProcessWeaponFireAsync_WithInvalidAttacker_ShouldReturnFailure()
        {
            // Arrange
            string attackerId = "invalid-player";
            string weaponId = "weapon-1";
            var targetPosition = new Vector3(10, 0, 10);

            _mockPlayerManager.Setup(x => x.GetPlayer(attackerId)).Returns((Player?)null);

            // Act
            var result = await _combatSystem.ProcessWeaponFireAsync(attackerId, weaponId, targetPosition);

            // Assert
            Assert.False(result.Success);
            Assert.Equal("Attacker not found", result.Message);
        }

        [Fact]
        public async Task ProcessWeaponFireAsync_WithValidAttacker_ShouldReturnResult()
        {
            // Arrange
            string attackerId = "valid-player";
            string weaponId = "weapon-1";
            var targetPosition = new Vector3(10, 0, 10);
            
            var attacker = new Player 
            { 
                PlayerId = attackerId,
                Position = new Vector3(0, 0, 0)
            };

            _mockPlayerManager.Setup(x => x.GetPlayer(attackerId)).Returns(attacker);

            // Act
            var result = await _combatSystem.ProcessWeaponFireAsync(attackerId, weaponId, targetPosition);

            // Assert
            Assert.True(result.Success);
            Assert.True(_combatSystem.IsPlayerInCombat(attackerId));
        }

        [Fact]
        public async Task EndCombatAsync_AfterStartCombat_ShouldWork()
        {
            // Arrange
            string playerId = "test-player";
            var player = new Player { PlayerId = playerId };
            _mockPlayerManager.Setup(x => x.GetPlayer(playerId)).Returns(player);

            // Start combat
            await _combatSystem.StartCombatAsync(playerId);
            Assert.True(_combatSystem.IsPlayerInCombat(playerId));

            // Act - Manually end combat
            await _combatSystem.EndCombatAsync(playerId);

            // Assert
            Assert.False(_combatSystem.IsPlayerInCombat(playerId));
        }
    }
}
