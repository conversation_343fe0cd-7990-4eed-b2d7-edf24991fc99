using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using MMO.Server.Configuration;
using Xunit;

namespace MMO.Server.Tests.Configuration
{
    public class ConfigurationTests
    {
        [Fact]
        public void DynamoDbConfiguration_ShouldBindFromConfiguration()
        {
            // Arrange
            var configurationData = new Dictionary<string, string>
            {
                {"DynamoDb:PlayersTableName", "test-players"},
                {"DynamoDb:WorldObjectsTableName", "test-world-objects"},
                {"DynamoDb:ZoneRegistryTableName", "test-zone-registry"},
                {"DynamoDb:BatchSaveIntervalSeconds", "10"},
                {"DynamoDb:MaxBatchSize", "50"}
            };

            var configuration = new ConfigurationBuilder()
                .AddInMemoryCollection(configurationData!)
                .Build();

            var services = new ServiceCollection();
            services.Configure<DynamoDbConfiguration>(
                configuration.GetSection(DynamoDbConfiguration.SectionName));

            var serviceProvider = services.BuildServiceProvider();

            // Act
            var options = serviceProvider.GetRequiredService<IOptions<DynamoDbConfiguration>>();
            var config = options.Value;

            // Assert
            Assert.Equal("test-players", config.PlayersTableName);
            Assert.Equal("test-world-objects", config.WorldObjectsTableName);
            Assert.Equal("test-zone-registry", config.ZoneRegistryTableName);
            Assert.Equal(10, config.BatchSaveIntervalSeconds);
            Assert.Equal(50, config.MaxBatchSize);
        }

        [Fact]
        public void GameServerConfiguration_ShouldBindFromConfiguration()
        {
            // Arrange
            var configurationData = new Dictionary<string, string>
            {
                {"GameServer:ZoneId", "test-zone"},
                {"GameServer:ServerPort", "8888"},
                {"GameServer:HeartbeatIntervalSeconds", "60"},
                {"GameServer:GameLoopUpdateRate", "30.0"},
                {"GameServer:MaxPlayers", "200"},
                {"GameServer:Region", "us-west-2"},
                {"GameServer:EnableDebugLogging", "true"}
            };

            var configuration = new ConfigurationBuilder()
                .AddInMemoryCollection(configurationData!)
                .Build();

            var services = new ServiceCollection();
            services.Configure<GameServerConfiguration>(
                configuration.GetSection(GameServerConfiguration.SectionName));

            var serviceProvider = services.BuildServiceProvider();

            // Act
            var options = serviceProvider.GetRequiredService<IOptions<GameServerConfiguration>>();
            var config = options.Value;

            // Assert
            Assert.Equal("test-zone", config.ZoneId);
            Assert.Equal(8888, config.ServerPort);
            Assert.Equal(60, config.HeartbeatIntervalSeconds);
            Assert.Equal(30.0f, config.GameLoopUpdateRate);
            Assert.Equal(200, config.MaxPlayers);
            Assert.Equal("us-west-2", config.Region);
            Assert.True(config.EnableDebugLogging);
        }

        [Fact]
        public void DynamoDbConfiguration_ShouldUseDefaultValues()
        {
            // Arrange
            var configuration = new ConfigurationBuilder().Build();
            var services = new ServiceCollection();
            services.Configure<DynamoDbConfiguration>(
                configuration.GetSection(DynamoDbConfiguration.SectionName));

            var serviceProvider = services.BuildServiceProvider();

            // Act
            var options = serviceProvider.GetRequiredService<IOptions<DynamoDbConfiguration>>();
            var config = options.Value;

            // Assert
            Assert.Equal("reapers-passing-players", config.PlayersTableName);
            Assert.Equal("reapers-passing-world-objects", config.WorldObjectsTableName);
            Assert.Equal("reapers-passing-zone-registry", config.ZoneRegistryTableName);
            Assert.Equal(5, config.BatchSaveIntervalSeconds);
            Assert.Equal(25, config.MaxBatchSize);
        }

        [Fact]
        public void GameServerConfiguration_ShouldUseDefaultValues()
        {
            // Arrange
            var configuration = new ConfigurationBuilder().Build();
            var services = new ServiceCollection();
            services.Configure<GameServerConfiguration>(
                configuration.GetSection(GameServerConfiguration.SectionName));

            var serviceProvider = services.BuildServiceProvider();

            // Act
            var options = serviceProvider.GetRequiredService<IOptions<GameServerConfiguration>>();
            var config = options.Value;

            // Assert
            Assert.Equal("zone-1", config.ZoneId);
            Assert.Equal(7777, config.ServerPort);
            Assert.Equal(30, config.HeartbeatIntervalSeconds);
            Assert.Equal(20.0f, config.GameLoopUpdateRate);
            Assert.Equal(100, config.MaxPlayers);
            Assert.Equal("us-east-1", config.Region);
            Assert.False(config.EnableDebugLogging);
        }
    }
}
