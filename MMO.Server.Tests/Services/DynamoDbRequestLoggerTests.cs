using Amazon.DynamoDBv2.Model;
using Microsoft.Extensions.Logging;
using MMO.Server.Configuration;
using MMO.Server.Services;
using Moq;
using System.Text.Json;
using Xunit;

namespace MMO.Server.Tests.Services
{
    public class DynamoDbRequestLoggerTests
    {
        private readonly Mock<ILogger> _mockLogger;
        private readonly DynamoDbConfiguration _config;

        public DynamoDbRequestLoggerTests()
        {
            _mockLogger = new Mock<ILogger>();
            _config = new DynamoDbConfiguration
            {
                EnableRequestLogging = true,
                EnableResponseLogging = true,
                RequestLogLevel = "Information"
            };
        }

        [Fact]
        public void LogRequest_WhenLoggingEnabled_ShouldLogRequest()
        {
            // Arrange
            var request = new PutItemRequest
            {
                TableName = "test-table",
                Item = new Dictionary<string, AttributeValue>
                {
                    { "id", new AttributeValue { S = "test-id" } },
                    { "name", new AttributeValue { S = "test-name" } }
                }
            };

            // Act
            DynamoDbRequestLogger.LogRequest(_mockLogger.Object, _config, "PutItem", request);

            // Assert
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("DynamoDB PutItem Request")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }

        [Fact]
        public void LogRequest_WhenLoggingDisabled_ShouldNotLog()
        {
            // Arrange
            _config.EnableRequestLogging = false;
            var request = new PutItemRequest { TableName = "test-table" };

            // Act
            DynamoDbRequestLogger.LogRequest(_mockLogger.Object, _config, "PutItem", request);

            // Assert
            _mockLogger.Verify(
                x => x.Log(
                    It.IsAny<LogLevel>(),
                    It.IsAny<EventId>(),
                    It.IsAny<It.IsAnyType>(),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Never);
        }

        [Fact]
        public void LogRequestItems_ShouldLogItemsAsJson()
        {
            // Arrange
            var items = new Dictionary<string, AttributeValue>
            {
                { "player_id", new AttributeValue { S = "player-123" } },
                { "level", new AttributeValue { N = "42" } },
                { "is_active", new AttributeValue { BOOL = true } }
            };

            // Act
            DynamoDbRequestLogger.LogRequestItems(_mockLogger.Object, _config, "SavePlayer", items, "players-table");

            // Assert
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("DynamoDB SavePlayer Items for table players-table")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }

        [Fact]
        public void LogKeyItems_ShouldLogKeyAsJson()
        {
            // Arrange
            var key = new Dictionary<string, AttributeValue>
            {
                { "player_id", new AttributeValue { S = "player-123" } }
            };

            // Act
            DynamoDbRequestLogger.LogKeyItems(_mockLogger.Object, _config, "LoadPlayer", key, "players-table");

            // Assert
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("DynamoDB LoadPlayer Key for table players-table")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }

        [Fact]
        public void LogUpdateExpression_ShouldLogUpdateExpressionAsJson()
        {
            // Arrange
            var updateExpression = "SET #status = :status, last_updated = :timestamp";
            var attributeValues = new Dictionary<string, AttributeValue>
            {
                { ":status", new AttributeValue { S = "active" } },
                { ":timestamp", new AttributeValue { N = "1234567890" } }
            };
            var attributeNames = new Dictionary<string, string>
            {
                { "#status", "status" }
            };

            // Act
            DynamoDbRequestLogger.LogUpdateExpression(_mockLogger.Object, _config, "UpdatePlayer", 
                updateExpression, attributeValues, attributeNames, "players-table");

            // Assert
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("DynamoDB UpdatePlayer Update Expression for table players-table")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }

        [Theory]
        [InlineData("Debug", LogLevel.Debug)]
        [InlineData("Information", LogLevel.Information)]
        [InlineData("Warning", LogLevel.Warning)]
        [InlineData("Error", LogLevel.Error)]
        [InlineData("invalid", LogLevel.Debug)]
        public void LogRequest_ShouldUseCorrectLogLevel(string configLogLevel, LogLevel expectedLogLevel)
        {
            // Arrange
            _config.RequestLogLevel = configLogLevel;
            var request = new PutItemRequest { TableName = "test-table" };

            // Act
            DynamoDbRequestLogger.LogRequest(_mockLogger.Object, _config, "PutItem", request);

            // Assert
            _mockLogger.Verify(
                x => x.Log(
                    expectedLogLevel,
                    It.IsAny<EventId>(),
                    It.IsAny<It.IsAnyType>(),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }

        [Fact]
        public void AttributeValueJsonConverter_ShouldSerializeStringValue()
        {
            // Arrange
            var attributeValue = new AttributeValue { S = "test-string" };
            var options = new JsonSerializerOptions
            {
                Converters = { new AttributeValueJsonConverter() }
            };

            // Act
            var json = JsonSerializer.Serialize(attributeValue, options);

            // Assert
            Assert.Contains("\"S\":\"test-string\"", json);
        }

        [Fact]
        public void AttributeValueJsonConverter_ShouldSerializeNumberValue()
        {
            // Arrange
            var attributeValue = new AttributeValue { N = "42" };
            var options = new JsonSerializerOptions
            {
                Converters = { new AttributeValueJsonConverter() }
            };

            // Act
            var json = JsonSerializer.Serialize(attributeValue, options);

            // Assert
            Assert.Contains("\"N\":\"42\"", json);
        }

        [Fact]
        public void AttributeValueJsonConverter_ShouldSerializeBooleanValue()
        {
            // Arrange
            var attributeValue = new AttributeValue { BOOL = true };
            var options = new JsonSerializerOptions
            {
                Converters = { new AttributeValueJsonConverter() }
            };

            // Act
            var json = JsonSerializer.Serialize(attributeValue, options);

            // Assert
            Assert.Contains("\"BOOL\":true", json);
        }

        [Fact]
        public void AttributeValueJsonConverter_ShouldSerializeStringSetValue()
        {
            // Arrange
            var attributeValue = new AttributeValue { SS = new List<string> { "value1", "value2" } };
            var options = new JsonSerializerOptions
            {
                Converters = { new AttributeValueJsonConverter() }
            };

            // Act
            var json = JsonSerializer.Serialize(attributeValue, options);

            // Assert
            Assert.Contains("\"SS\":[\"value1\",\"value2\"]", json);
        }
    }
}
