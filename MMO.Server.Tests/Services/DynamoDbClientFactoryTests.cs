using Amazon.DynamoDBv2;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MMO.Server.Configuration;
using MMO.Server.Services;
using Moq;
using Xunit;

namespace MMO.Server.Tests.Services
{
    public class DynamoDbClientFactoryTests
    {
        private readonly Mock<ILogger<DynamoDbClientFactory>> _mockLogger;

        public DynamoDbClientFactoryTests()
        {
            _mockLogger = new Mock<ILogger<DynamoDbClientFactory>>();
        }

        [Fact]
        public void CreateClient_WithDefaultSettings_ShouldCreateAwsClient()
        {
            // Arrange
            var config = new DynamoDbConfiguration
            {
                Region = "us-east-1"
            };
            var options = Options.Create(config);
            var factory = new DynamoDbClientFactory(_mockLogger.Object, options);

            // Act
            var client = factory.CreateClient();

            // Assert
            Assert.NotNull(client);
            Assert.IsType<AmazonDynamoDBClient>(client);
        }

        [Fact]
        public void CreateClient_WithServiceUrl_ShouldCreateLocalClient()
        {
            // Arrange
            var config = new DynamoDbConfiguration
            {
                Region = "USEast1",
                ServiceURL = "http://localhost:8000"
            };
            var options = Options.Create(config);
            var factory = new DynamoDbClientFactory(_mockLogger.Object, options);

            // Act
            var client = factory.CreateClient();

            // Assert
            Assert.NotNull(client);
            Assert.IsType<AmazonDynamoDBClient>(client);
        }

        [Fact]
        public void CreateClient_WithFauxCredentials_ShouldCreateLocalClient()
        {
            // Arrange
            var config = new DynamoDbConfiguration
            {
                Region = "USEast1",
                UseFauxCredentials = true
            };
            var options = Options.Create(config);
            var factory = new DynamoDbClientFactory(_mockLogger.Object, options);

            // Act
            var client = factory.CreateClient();

            // Assert
            Assert.NotNull(client);
            Assert.IsType<AmazonDynamoDBClient>(client);
        }

        [Fact]
        public void CreateClient_WithExplicitCredentials_ShouldCreateClientWithCredentials()
        {
            // Arrange
            var config = new DynamoDbConfiguration
            {
                Region = "us-east-1",
                AccessKeyId = "test-access-key",
                SecretAccessKey = "test-secret-key"
            };
            var options = Options.Create(config);
            var factory = new DynamoDbClientFactory(_mockLogger.Object, options);

            // Act
            var client = factory.CreateClient();

            // Assert
            Assert.NotNull(client);
            Assert.IsType<AmazonDynamoDBClient>(client);
        }

        [Fact]
        public void CreateClient_WithNoRegion_ShouldUseDefaultRegion()
        {
            // Arrange
            var config = new DynamoDbConfiguration();
            var options = Options.Create(config);
            var factory = new DynamoDbClientFactory(_mockLogger.Object, options);

            // Act
            var client = factory.CreateClient();

            // Assert
            Assert.NotNull(client);
            Assert.IsType<AmazonDynamoDBClient>(client);
            
            // Verify default region logging was called
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Using default DynamoDB region")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }

        [Fact]
        public void CreateClient_WithServiceUrl_ShouldLogLocalClientCreation()
        {
            // Arrange
            var config = new DynamoDbConfiguration
            {
                ServiceURL = "http://localhost:8000"
            };
            var options = Options.Create(config);
            var factory = new DynamoDbClientFactory(_mockLogger.Object, options);

            // Act
            var client = factory.CreateClient();

            // Assert
            Assert.NotNull(client);
            
            // Verify service URL logging was called
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Using DynamoDB service URL")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);

            // Verify local client creation logging was called
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Creating local DynamoDB client with faux credentials")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }
    }
}
