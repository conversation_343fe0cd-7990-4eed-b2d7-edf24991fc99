using Amazon.DynamoDBv2.Model;
using Microsoft.Extensions.Logging;
using MMO.Server.Configuration;
using MMO.Server.Services;
using System.Text;
using Xunit;
using Xunit.Abstractions;

namespace MMO.Server.Tests.Services
{
    public class DynamoDbRequestLoggerIntegrationTests
    {
        private readonly ITestOutputHelper _output;
        private readonly TestLogger _testLogger;
        private readonly DynamoDbConfiguration _config;

        public DynamoDbRequestLoggerIntegrationTests(ITestOutputHelper output)
        {
            _output = output;
            _testLogger = new TestLogger();
            _config = new DynamoDbConfiguration
            {
                EnableRequestLogging = true,
                EnableResponseLogging = true,
                RequestLogLevel = "Information"
            };
        }

        [Fact]
        public void LogRequestItems_ShouldOutputActualJsonContent()
        {
            // Arrange
            var items = new Dictionary<string, AttributeValue>
            {
                { "player_id", new AttributeValue { S = "player-12345" } },
                { "character_name", new AttributeValue { S = "CyberReaper" } },
                { "level", new AttributeValue { N = "42" } },
                { "experience_points", new AttributeValue { N = "125000" } },
                { "is_alive", new AttributeValue { BOOL = true } },
                { "credits", new AttributeValue { N = "5000" } },
                { "tags", new AttributeValue { SS = new List<string> { "veteran", "guild_leader" } } }
            };

            // Act
            DynamoDbRequestLogger.LogRequestItems(_testLogger, _config, "SavePlayer", items, "reapers-passing-players-dev");

            // Assert
            var logMessage = _testLogger.LastMessage;
            _output.WriteLine("Logged Message:");
            _output.WriteLine(logMessage);

            // Verify the message contains actual JSON content, not type names
            Assert.Contains("DynamoDB SavePlayer Items for table reapers-passing-players-dev:", logMessage);
            Assert.Contains("\"player_id\"", logMessage);
            Assert.Contains("\"S\": \"player-12345\"", logMessage);
            Assert.Contains("\"character_name\"", logMessage);
            Assert.Contains("\"S\": \"CyberReaper\"", logMessage);
            Assert.Contains("\"level\"", logMessage);
            Assert.Contains("\"N\": \"42\"", logMessage);
            Assert.Contains("\"BOOL\": true", logMessage);
            Assert.Contains("\"SS\": [", logMessage);
            Assert.Contains("\"veteran\"", logMessage);
            Assert.Contains("\"guild_leader\"", logMessage);
            
            // Verify it does NOT contain the type name
            Assert.DoesNotContain("System.Collections.Generic.Dictionary", logMessage);
            Assert.DoesNotContain("Amazon.DynamoDBv2.Model.AttributeValue", logMessage);
        }

        [Fact]
        public void LogKeyItems_ShouldOutputActualJsonContent()
        {
            // Arrange
            var key = new Dictionary<string, AttributeValue>
            {
                { "player_id", new AttributeValue { S = "player-67890" } }
            };

            // Act
            DynamoDbRequestLogger.LogKeyItems(_testLogger, _config, "LoadPlayer", key, "reapers-passing-players-dev");

            // Assert
            var logMessage = _testLogger.LastMessage;
            _output.WriteLine("Logged Message:");
            _output.WriteLine(logMessage);

            Assert.Contains("DynamoDB LoadPlayer Key for table reapers-passing-players-dev:", logMessage);
            Assert.Contains("\"player_id\"", logMessage);
            Assert.Contains("\"S\": \"player-67890\"", logMessage);
            Assert.DoesNotContain("System.Collections.Generic.Dictionary", logMessage);
        }

        [Fact]
        public void LogUpdateExpression_ShouldOutputActualJsonContent()
        {
            // Arrange
            var updateExpression = "SET last_heartbeat = :heartbeat, current_players = :players";
            var attributeValues = new Dictionary<string, AttributeValue>
            {
                { ":heartbeat", new AttributeValue { N = "1705315875" } },
                { ":players", new AttributeValue { N = "5" } }
            };
            var attributeNames = new Dictionary<string, string>
            {
                { "#status", "status" }
            };

            // Act
            DynamoDbRequestLogger.LogUpdateExpression(_testLogger, _config, "UpdateHeartbeat", 
                updateExpression, attributeValues, attributeNames, "reapers-passing-zone-registry-dev");

            // Assert
            var logMessage = _testLogger.LastMessage;
            _output.WriteLine("Logged Message:");
            _output.WriteLine(logMessage);

            Assert.Contains("DynamoDB UpdateHeartbeat Update Expression for table reapers-passing-zone-registry-dev:", logMessage);
            Assert.Contains("\"updateExpression\": \"SET last_heartbeat = :heartbeat, current_players = :players\"", logMessage);
            Assert.Contains("\":heartbeat\"", logMessage);
            Assert.Contains("\"N\": \"1705315875\"", logMessage);
            Assert.Contains("\":players\"", logMessage);
            Assert.Contains("\"N\": \"5\"", logMessage);
            Assert.Contains("\"#status\": \"status\"", logMessage);
        }

        private class TestLogger : ILogger
        {
            public string LastMessage { get; private set; } = string.Empty;

            public IDisposable? BeginScope<TState>(TState state) where TState : notnull => null;

            public bool IsEnabled(LogLevel logLevel) => true;

            public void Log<TState>(LogLevel logLevel, EventId eventId, TState state, Exception? exception, Func<TState, Exception?, string> formatter)
            {
                LastMessage = formatter(state, exception);
            }
        }
    }
}
