using Amazon.DynamoDBv2.Model;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MMO.Server.Configuration;
using MMO.Server.Services;
using MMO.Shared.Models.Networking;
using System.Text;
using Xunit;
using Xunit.Abstractions;

namespace MMO.Server.Tests.Services
{
    public class ZoneRegistryServiceIntegrationTests
    {
        private readonly ITestOutputHelper _output;
        private readonly TestLogger _testLogger;
        private readonly DynamoDbConfiguration _dynamoConfig;
        private readonly GameServerConfiguration _gameConfig;

        public ZoneRegistryServiceIntegrationTests(ITestOutputHelper output)
        {
            _output = output;
            _testLogger = new TestLogger();
            _dynamoConfig = new DynamoDbConfiguration
            {
                EnableRequestLogging = true,
                EnableResponseLogging = false,
                RequestLogLevel = "Information",
                ZoneRegistryTableName = "test-zone-registry"
            };
            _gameConfig = new GameServerConfiguration
            {
                MaxPlayers = 100,
                Region = "us-east-1"
            };
        }

        [Fact]
        public void RegisterZoneAsync_ShouldLogJsonContent_NotTypeName()
        {
            // Arrange
            var dynamoOptions = Options.Create(_dynamoConfig);
            var gameOptions = Options.Create(_gameConfig);
            
            // We can't actually call the method without a real DynamoDB connection,
            // but we can test the logging directly
            var items = new Dictionary<string, AttributeValue>
            {
                { "zone_id", new AttributeValue { S = "test-zone-1" } },
                { "zone_name", new AttributeValue { S = "Zone test-zone-1" } },
                { "server_address", new AttributeValue { S = "localhost" } },
                { "server_port", new AttributeValue { N = "7777" } },
                { "status", new AttributeValue { S = ServerStatus.Online.ToString() } },
                { "max_players", new AttributeValue { N = "100" } },
                { "current_players", new AttributeValue { N = "0" } },
                { "region", new AttributeValue { S = "us-east-1" } },
                { "last_heartbeat", new AttributeValue { N = "1705315875" } }
            };

            // Act
            DynamoDbRequestLogger.LogRequestItems(_testLogger, _dynamoConfig, "RegisterZone", items, "test-zone-registry");

            // Assert
            var logMessage = _testLogger.LastMessage;
            _output.WriteLine("Logged Message:");
            _output.WriteLine(logMessage);

            // Verify the message contains actual JSON content, not type names
            Assert.Contains("DynamoDB RegisterZone Items for table test-zone-registry:", logMessage);
            Assert.Contains("\"zone_id\"", logMessage);
            Assert.Contains("\"S\": \"test-zone-1\"", logMessage);
            Assert.Contains("\"zone_name\"", logMessage);
            Assert.Contains("\"S\": \"Zone test-zone-1\"", logMessage);
            Assert.Contains("\"server_address\"", logMessage);
            Assert.Contains("\"S\": \"localhost\"", logMessage);
            Assert.Contains("\"server_port\"", logMessage);
            Assert.Contains("\"N\": \"7777\"", logMessage);
            Assert.Contains("\"status\"", logMessage);
            Assert.Contains("\"S\": \"Online\"", logMessage);
            Assert.Contains("\"max_players\"", logMessage);
            Assert.Contains("\"N\": \"100\"", logMessage);
            Assert.Contains("\"current_players\"", logMessage);
            Assert.Contains("\"N\": \"0\"", logMessage);
            Assert.Contains("\"region\"", logMessage);
            Assert.Contains("\"S\": \"us-east-1\"", logMessage);
            Assert.Contains("\"last_heartbeat\"", logMessage);
            Assert.Contains("\"N\": \"1705315875\"", logMessage);
            
            // Verify it does NOT contain the type name that was causing the issue
            Assert.DoesNotContain("System.Collections.Generic.Dictionary", logMessage);
            Assert.DoesNotContain("Amazon.DynamoDBv2.Model.AttributeValue", logMessage);
        }

        [Fact]
        public void UpdateHeartbeatAsync_ShouldLogJsonContent_NotTypeName()
        {
            // Arrange
            var updateExpression = "SET last_heartbeat = :heartbeat, current_players = :players";
            var attributeValues = new Dictionary<string, AttributeValue>
            {
                { ":heartbeat", new AttributeValue { N = "1705315875" } },
                { ":players", new AttributeValue { N = "5" } }
            };

            // Act
            DynamoDbRequestLogger.LogUpdateExpression(_testLogger, _dynamoConfig, "UpdateHeartbeat", 
                updateExpression, attributeValues, null, "test-zone-registry");

            // Assert
            var logMessage = _testLogger.LastMessage;
            _output.WriteLine("Logged Message:");
            _output.WriteLine(logMessage);

            Assert.Contains("DynamoDB UpdateHeartbeat Update Expression for table test-zone-registry:", logMessage);
            Assert.Contains("\"updateExpression\": \"SET last_heartbeat = :heartbeat, current_players = :players\"", logMessage);
            Assert.Contains("\":heartbeat\"", logMessage);
            Assert.Contains("\"N\": \"1705315875\"", logMessage);
            Assert.Contains("\":players\"", logMessage);
            Assert.Contains("\"N\": \"5\"", logMessage);
            Assert.DoesNotContain("System.Collections.Generic.Dictionary", logMessage);
        }

        private class TestLogger : ILogger
        {
            public string LastMessage { get; private set; } = string.Empty;

            public IDisposable? BeginScope<TState>(TState state) where TState : notnull => null;

            public bool IsEnabled(LogLevel logLevel) => true;

            public void Log<TState>(LogLevel logLevel, EventId eventId, TState state, Exception? exception, Func<TState, Exception?, string> formatter)
            {
                LastMessage = formatter(state, exception);
            }
        }
    }
}
