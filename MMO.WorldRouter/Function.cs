using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Text.Json;
using System.Threading.Tasks;
using Amazon.DynamoDBv2;
using Amazon.DynamoDBv2.Model;
using Amazon.Lambda.APIGatewayEvents;
using Amazon.Lambda.Core;
using MMO.Shared.Models.Networking;

// Assembly attribute to enable the Lambda function's JSON input to be converted into a .NET class.
[assembly: LambdaSerializer(typeof(Amazon.Lambda.Serialization.SystemTextJson.DefaultLambdaJsonSerializer))]

namespace MMO.WorldRouter
{
    public class Function
    {
        private readonly IAmazonDynamoDB _dynamoDbClient;
        private readonly string _playersTableName;
        private readonly string _zoneRegistryTableName;

        /// <summary>
        /// Default constructor. This constructor is used by Lambda to construct the instance. When invoked in a Lambda environment
        /// the AWS credentials will come from the IAM role associated with the function and the AWS region will be set to the
        /// region the Lambda function is executed in.
        /// </summary>
        public Function()
        {
            _dynamoDbClient = new AmazonDynamoDBClient();
            _playersTableName = Environment.GetEnvironmentVariable("PLAYERS_TABLE_NAME") ?? "reapers-passing-players";
            _zoneRegistryTableName = Environment.GetEnvironmentVariable("ZONE_REGISTRY_TABLE_NAME") ?? "reapers-passing-zone-registry";
        }

        /// <summary>
        /// Constructs an instance with a preconfigured DynamoDB client. This can be used for testing the outside of the Lambda environment.
        /// </summary>
        /// <param name="dynamoDbClient">The DynamoDB client to use</param>
        /// <param name="playersTableName">The name of the players table</param>
        /// <param name="zoneRegistryTableName">The name of the zone registry table</param>
        public Function(IAmazonDynamoDB dynamoDbClient, string playersTableName, string zoneRegistryTableName)
        {
            _dynamoDbClient = dynamoDbClient;
            _playersTableName = playersTableName;
            _zoneRegistryTableName = zoneRegistryTableName;
        }

        /// <summary>
        /// A Lambda function to respond to HTTP Get methods from API Gateway
        /// </summary>
        /// <param name="request">The API Gateway request</param>
        /// <param name="context">The Lambda context</param>
        /// <returns>The API Gateway response</returns>
        public async Task<APIGatewayProxyResponse> FunctionHandler(APIGatewayProxyRequest request, ILambdaContext context)
        {
            context.Logger.LogInformation($"Processing request: {request.HttpMethod} {request.Path}");

            try
            {
                // Extract the JWT token from the Authorization header
                if (!request.Headers.TryGetValue("Authorization", out var authHeader) || string.IsNullOrEmpty(authHeader))
                {
                    return CreateErrorResponse(401, "Missing Authorization header");
                }

                // Remove "Bearer " prefix if present
                var token = authHeader.StartsWith("Bearer ") ? authHeader.Substring(7) : authHeader;

                // Extract player ID from JWT token
                var playerId = ExtractPlayerIdFromToken(token);
                if (string.IsNullOrEmpty(playerId))
                {
                    return CreateErrorResponse(401, "Invalid token: could not extract player ID");
                }

                context.Logger.LogInformation($"Processing request for player: {playerId}");

                // Get player's last known zone
                var lastKnownZoneId = await GetPlayerLastKnownZone(playerId);
                if (string.IsNullOrEmpty(lastKnownZoneId))
                {
                    // Default to zone-1 for new players
                    lastKnownZoneId = "zone-1";
                    context.Logger.LogInformation($"No last known zone for player {playerId}, defaulting to {lastKnownZoneId}");
                }

                // Get server connection info for the zone
                var connectionInfo = await GetZoneServerInfo(lastKnownZoneId);
                if (connectionInfo == null)
                {
                    return CreateErrorResponse(503, $"No available server for zone: {lastKnownZoneId}");
                }

                // Generate a connection token
                connectionInfo.ConnectionToken = GenerateConnectionToken(playerId, lastKnownZoneId);
                connectionInfo.TokenExpiry = DateTime.UtcNow.AddMinutes(5);

                context.Logger.LogInformation($"Returning server info for zone {lastKnownZoneId}: {connectionInfo.ServerAddress}:{connectionInfo.ServerPort}");

                // Return the connection information
                var responseBody = JsonSerializer.Serialize(connectionInfo);
                return new APIGatewayProxyResponse
                {
                    StatusCode = 200,
                    Body = responseBody,
                    Headers = new Dictionary<string, string>
                    {
                        { "Content-Type", "application/json" },
                        { "Access-Control-Allow-Origin", "*" },
                        { "Access-Control-Allow-Headers", "Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token" },
                        { "Access-Control-Allow-Methods", "GET,POST,OPTIONS" }
                    }
                };
            }
            catch (Exception ex)
            {
                context.Logger.LogError($"Error processing request: {ex.Message}");
                context.Logger.LogError($"Stack trace: {ex.StackTrace}");
                return CreateErrorResponse(500, "Internal server error");
            }
        }

        /// <summary>
        /// Extracts the player ID from a JWT token
        /// </summary>
        private string ExtractPlayerIdFromToken(string token)
        {
            try
            {
                var handler = new JwtSecurityTokenHandler();
                var jsonToken = handler.ReadJwtToken(token);

                // Try to get player_id from custom claims first
                if (jsonToken.Payload.TryGetValue("custom:player_id", out var playerIdClaim))
                {
                    return playerIdClaim.ToString();
                }

                // Fallback to subject claim
                return jsonToken.Subject;
            }
            catch (Exception)
            {
                return string.Empty;
            }
        }

        /// <summary>
        /// Gets the player's last known zone from DynamoDB
        /// </summary>
        private async Task<string> GetPlayerLastKnownZone(string playerId)
        {
            try
            {
                var request = new GetItemRequest
                {
                    TableName = _playersTableName,
                    Key = new Dictionary<string, AttributeValue>
                    {
                        { "player_id", new AttributeValue { S = playerId } }
                    },
                    ProjectionExpression = "last_known_zone_id"
                };

                var response = await _dynamoDbClient.GetItemAsync(request);

                if (response.Item.TryGetValue("last_known_zone_id", out var zoneIdAttribute))
                {
                    return zoneIdAttribute.S;
                }

                return string.Empty;
            }
            catch (Exception)
            {
                // If player doesn't exist or any error occurs, return empty string
                return string.Empty;
            }
        }

        /// <summary>
        /// Gets server connection information for a specific zone
        /// </summary>
        private async Task<ServerConnectionInfo> GetZoneServerInfo(string zoneId)
        {
            try
            {
                var request = new GetItemRequest
                {
                    TableName = _zoneRegistryTableName,
                    Key = new Dictionary<string, AttributeValue>
                    {
                        { "zone_id", new AttributeValue { S = zoneId } }
                    }
                };

                var response = await _dynamoDbClient.GetItemAsync(request);

                if (response.Item.Count == 0)
                {
                    return null;
                }

                var item = response.Item;
                return new ServerConnectionInfo
                {
                    ZoneId = zoneId,
                    ZoneName = item.TryGetValue("zone_name", out var zoneName) ? zoneName.S : zoneId,
                    ServerAddress = item.TryGetValue("server_address", out var address) ? address.S : "",
                    ServerPort = item.TryGetValue("server_port", out var port) && int.TryParse(port.N, out var portNum) ? portNum : 7777,
                    MaxPlayers = item.TryGetValue("max_players", out var maxPlayers) && int.TryParse(maxPlayers.N, out var maxNum) ? maxNum : 100,
                    CurrentPlayers = item.TryGetValue("current_players", out var currentPlayers) && int.TryParse(currentPlayers.N, out var currentNum) ? currentNum : 0,
                    Status = item.TryGetValue("status", out var status) ? Enum.Parse<ServerStatus>(status.S, true) : ServerStatus.Online,
                    Region = item.TryGetValue("region", out var region) ? region.S : "us-east-1",
                    LastHeartbeat = item.TryGetValue("last_heartbeat", out var heartbeat) && long.TryParse(heartbeat.N, out var heartbeatNum) 
                        ? DateTimeOffset.FromUnixTimeSeconds(heartbeatNum).DateTime 
                        : DateTime.UtcNow
                };
            }
            catch (Exception)
            {
                return null;
            }
        }

        /// <summary>
        /// Generates a connection token for the player
        /// </summary>
        private string GenerateConnectionToken(string playerId, string zoneId)
        {
            // In a real implementation, this would be a proper JWT or encrypted token
            // For now, we'll use a simple base64 encoded string with timestamp
            var tokenData = $"{playerId}:{zoneId}:{DateTimeOffset.UtcNow.ToUnixTimeSeconds()}";
            var tokenBytes = System.Text.Encoding.UTF8.GetBytes(tokenData);
            return Convert.ToBase64String(tokenBytes);
        }

        /// <summary>
        /// Creates an error response
        /// </summary>
        private APIGatewayProxyResponse CreateErrorResponse(int statusCode, string message)
        {
            var errorResponse = new { error = message };
            return new APIGatewayProxyResponse
            {
                StatusCode = statusCode,
                Body = JsonSerializer.Serialize(errorResponse),
                Headers = new Dictionary<string, string>
                {
                    { "Content-Type", "application/json" },
                    { "Access-Control-Allow-Origin", "*" },
                    { "Access-Control-Allow-Headers", "Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token" },
                    { "Access-Control-Allow-Methods", "GET,POST,OPTIONS" }
                }
            };
        }
    }
}
