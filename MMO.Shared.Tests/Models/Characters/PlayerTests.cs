using System;
using Xunit;
using MMO.Shared.Models.Characters;
using MMO.Shared.Models.Common;

namespace MMO.Shared.Tests.Models.Characters
{
    public class PlayerTests
    {
        [Fact]
        public void Constructor_InitializesWithDefaults()
        {
            // Act
            var player = new Player();
            
            // Assert
            Assert.Equal(1, player.Level);
            Assert.Equal(0, player.ExperiencePoints);
            Assert.Equal(GameConstants.Combat.BaseHealthPoints, player.CurrentHealth);
            Assert.Equal(GameConstants.Combat.BaseHealthPoints, player.MaxHealth);
            Assert.True(player.IsAlive);
            Assert.Equal(PlayerStatus.Online, player.Status);
            Assert.Equal(1000m, player.Credits);
        }
        
        [Fact]
        public void AddExperience_IncreasesExperience()
        {
            // Arrange
            var player = new Player();
            
            // Act
            bool leveledUp = player.AddExperience(500);
            
            // Assert
            Assert.Equal(500, player.ExperiencePoints);
            Assert.False(leveledUp); // Should not level up yet
            Assert.Equal(1, player.Level);
        }
        
        [Fact]
        public void AddExperience_CausesLevelUp()
        {
            // Arrange
            var player = new Player();
            
            // Act
            bool leveledUp = player.AddExperience(1000);
            
            // Assert
            Assert.Equal(1000, player.ExperiencePoints);
            Assert.True(leveledUp);
            Assert.Equal(2, player.Level);
            Assert.Equal(12, player.AvailableSkillPoints); // 10 starting + 2 from level up
        }
        
        [Fact]
        public void TakeDamage_ReducesHealth()
        {
            // Arrange
            var player = new Player();
            float initialHealth = player.CurrentHealth;
            
            // Act
            bool died = player.TakeDamage(25f);
            
            // Assert
            Assert.Equal(initialHealth - 25f, player.CurrentHealth);
            Assert.False(died);
            Assert.True(player.IsAlive);
        }
        
        [Fact]
        public void TakeDamage_CausesDeath()
        {
            // Arrange
            var player = new Player();
            
            // Act
            bool died = player.TakeDamage(150f); // More than max health
            
            // Assert
            Assert.Equal(0f, player.CurrentHealth);
            Assert.True(died);
            Assert.False(player.IsAlive);
            Assert.Equal(PlayerStatus.Dead, player.Status);
        }
        
        [Fact]
        public void Heal_IncreasesHealth()
        {
            // Arrange
            var player = new Player();
            player.TakeDamage(50f);
            float damagedHealth = player.CurrentHealth;
            
            // Act
            player.Heal(25f);
            
            // Assert
            Assert.Equal(damagedHealth + 25f, player.CurrentHealth);
            Assert.True(player.IsAlive);
        }
        
        [Fact]
        public void Heal_DoesNotExceedMaxHealth()
        {
            // Arrange
            var player = new Player();
            player.TakeDamage(10f);
            
            // Act
            player.Heal(50f); // More than needed
            
            // Assert
            Assert.Equal(player.MaxHealth, player.CurrentHealth);
        }
        
        [Fact]
        public void Heal_RevivesDeadPlayer()
        {
            // Arrange
            var player = new Player();
            player.TakeDamage(150f); // Kill the player
            
            // Act
            player.Heal(50f);
            
            // Assert
            Assert.Equal(50f, player.CurrentHealth);
            Assert.True(player.IsAlive);
            Assert.Equal(PlayerStatus.Online, player.Status);
        }
        
        [Fact]
        public void HasSkill_ReturnsTrueWhenSkillExists()
        {
            // Arrange
            var player = new Player();
            player.Skills["Firearms"] = 75;
            
            // Act & Assert
            Assert.True(player.HasSkill("Firearms", 50));
            Assert.True(player.HasSkill("Firearms", 75));
            Assert.False(player.HasSkill("Firearms", 80));
        }
        
        [Fact]
        public void HasSkill_ReturnsFalseWhenSkillDoesNotExist()
        {
            // Arrange
            var player = new Player();
            
            // Act & Assert
            Assert.False(player.HasSkill("NonexistentSkill", 1));
        }
        
        [Fact]
        public void MoveSpeed_CalculatesCorrectly()
        {
            // Arrange
            var player = new Player();
            player.Attributes.Agility = 15; // 5 above base
            
            // Act
            float moveSpeed = player.MoveSpeed;
            
            // Assert
            float expected = GameConstants.Combat.BaseMoveSpeed * (1f + (15 - 10) * 0.1f);
            Assert.Equal(expected, moveSpeed);
        }
        
        [Fact]
        public void CarryCapacity_CalculatesCorrectly()
        {
            // Arrange
            var player = new Player();
            player.Attributes.Strength = 15;
            
            // Act
            float capacity = player.CarryCapacity;
            
            // Assert
            Assert.Equal(50f + (15 * 5f), capacity);
        }
    }
}
