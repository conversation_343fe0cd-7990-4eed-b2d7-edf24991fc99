using System;
using Xunit;
using MMO.Shared.Models.Items;

namespace MMO.Shared.Tests.Models.Items
{
    public class WeaponTests
    {
        [Fact]
        public void Constructor_InitializesWithDefaults()
        {
            // Act
            var weapon = new Weapon();
            
            // Assert
            Assert.Equal(ItemType.Weapon, weapon.Type);
            Assert.Equal(5, weapon.MaxModSlots);
            Assert.Equal(30, weapon.MagazineSize);
            Assert.Equal(30, weapon.CurrentAmmo);
        }
        
        [Fact]
        public void CalculateDamageAtRange_WithinEffectiveRange()
        {
            // Arrange
            var weapon = new Weapon
            {
                BaseDamage = 50f,
                EffectiveRange = 100f,
                IsEnergyWeapon = false
            };
            
            // Act
            float damage = weapon.CalculateDamageAtRange(50f);
            
            // Assert
            Assert.Equal(50f, damage);
        }
        
        [Fact]
        public void CalculateDamageAtRange_BeyondEffectiveRange()
        {
            // Arrange
            var weapon = new Weapon
            {
                BaseDamage = 50f,
                EffectiveRange = 100f,
                MaxRange = 200f,
                IsEnergyWeapon = false
            };
            
            // Act
            float damage = weapon.CalculateDamageAtRange(150f); // 50m beyond effective range
            
            // Assert
            Assert.True(damage < 50f); // Should be reduced
            Assert.True(damage >= 5f); // Should not go below 10% of base damage
        }
        
        [Fact]
        public void CalculateAccuracyAtRange_WithinEffectiveRange()
        {
            // Arrange
            var weapon = new Weapon
            {
                BaseAccuracy = 0.8f,
                EffectiveRange = 100f
            };
            
            // Act
            float accuracy = weapon.CalculateAccuracyAtRange(50f);
            
            // Assert
            Assert.Equal(0.8f, accuracy);
        }
        
        [Fact]
        public void CalculateAccuracyAtRange_BeyondEffectiveRange()
        {
            // Arrange
            var weapon = new Weapon
            {
                BaseAccuracy = 0.8f,
                EffectiveRange = 100f,
                AccuracyFalloff = 0.01f // 1% per meter
            };
            
            // Act
            float accuracy = weapon.CalculateAccuracyAtRange(150f); // 50m beyond effective
            
            // Assert
            Assert.Equal(0.3f, accuracy); // 0.8 - (50 * 0.01) = 0.3
        }
        
        [Fact]
        public void CalculateAccuracyAtRange_WithMovementPenalty()
        {
            // Arrange
            var weapon = new Weapon
            {
                BaseAccuracy = 0.8f,
                MovementAccuracyPenalty = 0.2f
            };
            
            // Act
            float accuracy = weapon.CalculateAccuracyAtRange(50f, isMoving: true);
            
            // Assert
            Assert.Equal(0.6f, accuracy); // 0.8 - 0.2 = 0.6
        }
        
        [Fact]
        public void CalculateAccuracyAtRange_WithScope()
        {
            // Arrange
            var weapon = new Weapon
            {
                BaseAccuracy = 0.8f,
                HasScope = true
            };
            
            // Act
            float accuracy = weapon.CalculateAccuracyAtRange(50f);
            
            // Assert
            Assert.Equal(0.9f, accuracy, 3); // 0.8 + 0.1 = 0.9, with 3 decimal precision
        }
        
        [Fact]
        public void Fire_WithNoAmmo_ReturnsFalse()
        {
            // Arrange
            var weapon = new Weapon { CurrentAmmo = 0 };
            
            // Act
            var result = weapon.Fire(50f);
            
            // Assert
            Assert.False(result.Success);
            Assert.Equal("No ammunition", result.Reason);
        }
        
        [Fact]
        public void Fire_WithAmmo_ConsumesAmmo()
        {
            // Arrange
            var weapon = new Weapon { CurrentAmmo = 10 };
            
            // Act
            var result = weapon.Fire(50f);
            
            // Assert
            Assert.True(result.Success);
            Assert.Equal(9, weapon.CurrentAmmo);
        }
        
        [Fact]
        public void Reload_IncreasesAmmoToMax()
        {
            // Arrange
            var weapon = new Weapon 
            { 
                CurrentAmmo = 5,
                MagazineSize = 30
            };
            
            // Act
            bool reloaded = weapon.Reload();
            
            // Assert
            Assert.True(reloaded);
            Assert.Equal(30, weapon.CurrentAmmo);
        }
        
        [Fact]
        public void Reload_WhenFull_ReturnsFalse()
        {
            // Arrange
            var weapon = new Weapon 
            { 
                CurrentAmmo = 30,
                MagazineSize = 30
            };
            
            // Act
            bool reloaded = weapon.Reload();
            
            // Assert
            Assert.False(reloaded);
        }
        
        [Fact]
        public void GetEffectiveFireRate_WithMods()
        {
            // Arrange
            var weapon = new Weapon { FireRate = 10f };
            var mod = new ItemMod { FireRateModifier = 0.2f }; // 20% increase
            weapon.AddModification(mod);
            
            // Act
            float effectiveRate = weapon.GetEffectiveFireRate();
            
            // Assert
            Assert.Equal(12f, effectiveRate); // 10 * (1 + 0.2) = 12
        }
        
        [Fact]
        public void GetEffectiveReloadTime_WithMods()
        {
            // Arrange
            var weapon = new Weapon { ReloadTime = 2f };
            var mod = new ItemMod { ReloadSpeedModifier = 0.3f }; // 30% faster
            weapon.AddModification(mod);
            
            // Act
            float effectiveTime = weapon.GetEffectiveReloadTime();
            
            // Assert
            Assert.Equal(1.4f, effectiveTime); // 2 * (1 - 0.3) = 1.4
        }
    }
}
