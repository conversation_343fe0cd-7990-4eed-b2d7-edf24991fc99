using Xunit;
using MMO.Shared.Models.Common;

namespace MMO.Shared.Tests.Models.Common
{
    public class Vector3Tests
    {
        [Fact]
        public void Constructor_SetsCorrectValues()
        {
            // Arrange & Act
            var vector = new Vector3(1f, 2f, 3f);
            
            // Assert
            Assert.Equal(1f, vector.X);
            Assert.Equal(2f, vector.Y);
            Assert.Equal(3f, vector.Z);
        }
        
        [Fact]
        public void Magnitude_CalculatesCorrectly()
        {
            // Arrange
            var vector = new Vector3(3f, 4f, 0f);
            
            // Act
            float magnitude = vector.Magnitude;
            
            // Assert
            Assert.Equal(5f, magnitude, 3); // 3-4-5 triangle
        }
        
        [Fact]
        public void Distance_CalculatesCorrectly()
        {
            // Arrange
            var a = new Vector3(0f, 0f, 0f);
            var b = new Vector3(3f, 4f, 0f);
            
            // Act
            float distance = Vector3.Distance(a, b);
            
            // Assert
            Assert.Equal(5f, distance, 3);
        }
        
        [Fact]
        public void Addition_WorksCorrectly()
        {
            // Arrange
            var a = new Vector3(1f, 2f, 3f);
            var b = new Vector3(4f, 5f, 6f);
            
            // Act
            var result = a + b;
            
            // Assert
            Assert.Equal(5f, result.X);
            Assert.Equal(7f, result.Y);
            Assert.Equal(9f, result.Z);
        }
        
        [Fact]
        public void Subtraction_WorksCorrectly()
        {
            // Arrange
            var a = new Vector3(5f, 7f, 9f);
            var b = new Vector3(1f, 2f, 3f);
            
            // Act
            var result = a - b;
            
            // Assert
            Assert.Equal(4f, result.X);
            Assert.Equal(5f, result.Y);
            Assert.Equal(6f, result.Z);
        }
        
        [Fact]
        public void ScalarMultiplication_WorksCorrectly()
        {
            // Arrange
            var vector = new Vector3(1f, 2f, 3f);
            
            // Act
            var result = vector * 2f;
            
            // Assert
            Assert.Equal(2f, result.X);
            Assert.Equal(4f, result.Y);
            Assert.Equal(6f, result.Z);
        }
        
        [Fact]
        public void Normalized_ReturnsUnitVector()
        {
            // Arrange
            var vector = new Vector3(3f, 4f, 0f);
            
            // Act
            var normalized = vector.Normalized;
            
            // Assert
            Assert.Equal(1f, normalized.Magnitude, 3);
            Assert.Equal(0.6f, normalized.X, 3);
            Assert.Equal(0.8f, normalized.Y, 3);
        }
        
        [Fact]
        public void DotProduct_CalculatesCorrectly()
        {
            // Arrange
            var a = new Vector3(1f, 2f, 3f);
            var b = new Vector3(4f, 5f, 6f);
            
            // Act
            float dot = Vector3.Dot(a, b);
            
            // Assert
            Assert.Equal(32f, dot); // 1*4 + 2*5 + 3*6 = 4 + 10 + 18 = 32
        }
        
        [Fact]
        public void CrossProduct_CalculatesCorrectly()
        {
            // Arrange
            var a = new Vector3(1f, 0f, 0f);
            var b = new Vector3(0f, 1f, 0f);
            
            // Act
            var cross = Vector3.Cross(a, b);
            
            // Assert
            Assert.Equal(0f, cross.X);
            Assert.Equal(0f, cross.Y);
            Assert.Equal(1f, cross.Z);
        }
        
        [Fact]
        public void Equality_WorksCorrectly()
        {
            // Arrange
            var a = new Vector3(1f, 2f, 3f);
            var b = new Vector3(1f, 2f, 3f);
            var c = new Vector3(1f, 2f, 4f);
            
            // Act & Assert
            Assert.True(a == b);
            Assert.False(a == c);
            Assert.True(a.Equals(b));
            Assert.False(a.Equals(c));
        }
    }
}
