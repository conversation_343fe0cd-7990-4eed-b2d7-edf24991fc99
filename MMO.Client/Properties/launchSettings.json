{"profiles": {"MMO.Client": {"commandName": "Project", "dotnetRunMessages": true, "environmentVariables": {"DOTNET_ENVIRONMENT": "Development"}}, "MMO.Client (Debug)": {"commandName": "Project", "dotnetRunMessages": true, "environmentVariables": {"DOTNET_ENVIRONMENT": "Development", "API_BASE_URL": "http://localhost:8080", "GAME_SERVER_HOST": "localhost", "GAME_SERVER_PORT": "7778", "CLIENT_DEBUG_MODE": "true", "CLIENT_LOG_LEVEL": "Debug"}}, "MMO.Client (Local Server)": {"commandName": "Project", "dotnetRunMessages": true, "environmentVariables": {"DOTNET_ENVIRONMENT": "Development", "API_BASE_URL": "http://localhost:7778", "GAME_SERVER_HOST": "localhost", "GAME_SERVER_PORT": "7778", "CLIENT_DEBUG_MODE": "true", "CLIENT_LOG_LEVEL": "Debug"}}, "MMO.Client (Production)": {"commandName": "Project", "dotnetRunMessages": true, "environmentVariables": {"DOTNET_ENVIRONMENT": "Production", "API_BASE_URL": "https://api.reapers-passing.com", "GAME_SERVER_HOST": "game.reapers-passing.com", "GAME_SERVER_PORT": "7777", "CLIENT_DEBUG_MODE": "false", "CLIENT_LOG_LEVEL": "Information"}}, "Docker": {"commandName": "<PERSON>er"}}}