using Microsoft.Extensions.Logging;
using MMO.Shared.Models.Characters;
using MMO.Shared.Models.Common;
using MMO.Shared.Models.Items;
using MMO.Shared.Models.Networking;

namespace MMO.Client.Services
{
    /// <summary>
    /// Main game client for connecting to and communicating with the game server
    /// </summary>
    public class GameClient : IGameClient
    {
        private readonly IApiClient _apiClient;
        private readonly ILogger<GameClient> _logger;
        private ServerConnectionInfo? _connectionInfo;
        private Timer? _heartbeatTimer;

        public bool IsConnected { get; private set; }
        public Player? CurrentPlayer { get; private set; }

        public event EventHandler<NetworkPacket>? PacketReceived;
        public event EventHandler<bool>? ConnectionStatusChanged;

        public GameClient(IApiClient apiClient, ILogger<GameClient> logger)
        {
            _apiClient = apiClient;
            _logger = logger;
        }

        public async Task<bool> ConnectAsync(string username, string password)
        {
            try
            {
                _logger.LogInformation("Connecting to game server...");

                // Get server connection info from API
                _connectionInfo = await _apiClient.ConnectAsync(username, password);
                if (_connectionInfo == null)
                {
                    _logger.LogError("Failed to get server connection info");
                    return false;
                }

                // Create a demo player for simulation
                CurrentPlayer = CreateDemoPlayer(username);

                // In a real implementation, this would establish a TCP/UDP connection
                // to the game server using the connection info
                _logger.LogInformation("Simulating connection to game server at {Address}:{Port}", 
                    _connectionInfo.ServerAddress, _connectionInfo.ServerPort);

                IsConnected = true;
                ConnectionStatusChanged?.Invoke(this, true);

                // Start heartbeat timer
                _heartbeatTimer = new Timer(SendHeartbeat, null, TimeSpan.Zero, TimeSpan.FromSeconds(30));

                _logger.LogInformation("Successfully connected to game server");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error connecting to game server");
                return false;
            }
        }

        public async Task DisconnectAsync()
        {
            try
            {
                _logger.LogInformation("Disconnecting from game server...");

                _heartbeatTimer?.Dispose();
                _heartbeatTimer = null;

                IsConnected = false;
                CurrentPlayer = null;
                _connectionInfo = null;

                ConnectionStatusChanged?.Invoke(this, false);

                _logger.LogInformation("Disconnected from game server");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error disconnecting from game server");
            }
        }

        public async Task SendMovementAsync(Vector3 position, Vector3 rotation)
        {
            if (!IsConnected || CurrentPlayer == null) return;

            try
            {
                var packet = new PlayerMovementPacket
                {
                    SenderId = CurrentPlayer.PlayerId,
                    Position = position,
                    Rotation = rotation,
                    Velocity = new Vector3(0, 0, 0),
                    IsGrounded = true,
                    State = MovementState.Walking
                };

                // Update local player position
                CurrentPlayer.Position = position;
                CurrentPlayer.Rotation = rotation;

                _logger.LogDebug("Sent movement update: {Position}", position);

                // Simulate receiving the packet back from server
                PacketReceived?.Invoke(this, packet);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending movement update");
            }
        }

        public async Task SendFireWeaponAsync(string weaponId, Vector3 targetPosition, string? targetId = null)
        {
            if (!IsConnected || CurrentPlayer == null) return;

            try
            {
                var packet = new FireWeaponPacket
                {
                    SenderId = CurrentPlayer.PlayerId,
                    WeaponId = weaponId,
                    FirePosition = CurrentPlayer.Position,
                    FireDirection = (targetPosition - CurrentPlayer.Position).Normalized,
                    TargetId = targetId ?? string.Empty,
                    Damage = 25f,
                    DamageType = DamageType.Ballistic,
                    IsCritical = false
                };

                _logger.LogInformation("Fired weapon {WeaponId} at {TargetPosition}", weaponId, targetPosition);

                // Simulate receiving the packet back from server
                PacketReceived?.Invoke(this, packet);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error firing weapon");
            }
        }

        public async Task SendUseAbilityAsync(string abilityId, Vector3 targetPosition, string? targetId = null)
        {
            if (!IsConnected || CurrentPlayer == null) return;

            try
            {
                var packet = new UseAbilityPacket
                {
                    SenderId = CurrentPlayer.PlayerId,
                    AbilityId = abilityId,
                    TargetPosition = targetPosition,
                    TargetId = targetId ?? string.Empty,
                    SystemPowerCost = 10f
                };

                _logger.LogInformation("Used ability {AbilityId} at {TargetPosition}", abilityId, targetPosition);

                // Simulate receiving the packet back from server
                PacketReceived?.Invoke(this, packet);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error using ability");
            }
        }

        public async Task SendChatMessageAsync(string message, ChatChannel channel, string? recipientId = null)
        {
            if (!IsConnected || CurrentPlayer == null) return;

            try
            {
                var packet = new ChatMessagePacket
                {
                    SenderId = CurrentPlayer.PlayerId,
                    Message = message,
                    Channel = channel,
                    RecipientId = recipientId ?? string.Empty
                };

                _logger.LogInformation("Sent chat message: [{Channel}] {Message}", channel, message);

                // Simulate receiving the packet back from server
                PacketReceived?.Invoke(this, packet);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending chat message");
            }
        }

        private async void SendHeartbeat(object? state)
        {
            try
            {
                if (IsConnected)
                {
                    var success = await _apiClient.SendHeartbeatAsync();
                    if (!success)
                    {
                        _logger.LogWarning("Heartbeat failed");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending heartbeat");
            }
        }

        private Player CreateDemoPlayer(string username)
        {
            return new Player
            {
                PlayerId = $"player_{username}_{DateTime.UtcNow.Ticks}",
                UserId = $"user_{username}",
                CharacterName = username,
                Level = 1,
                ExperiencePoints = 0,
                CurrentHealth = GameConstants.Combat.BaseHealthPoints,
                MaxHealth = GameConstants.Combat.BaseHealthPoints,
                CurrentSystemPower = GameConstants.Cybernetics.BaseSystemPower,
                MaxSystemPower = GameConstants.Cybernetics.BaseSystemPower,
                CurrentHumanity = GameConstants.Cybernetics.BaseHumanity,
                MaxHumanity = GameConstants.Cybernetics.BaseHumanity,
                Position = new Vector3(0, 0, 0),
                Rotation = new Vector3(0, 0, 0),
                Credits = 1000m,
                Status = PlayerStatus.Online,
                LastLoginAt = DateTime.UtcNow,
                CurrentZoneId = "zone-1",
                LastKnownZoneId = "zone-1"
            };
        }

        public void Dispose()
        {
            _heartbeatTimer?.Dispose();
        }
    }
}
