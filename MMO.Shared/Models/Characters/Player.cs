using System;
using System.Collections.Generic;
using MMO.Shared.Models.Common;
using MMO.Shared.Models.Systems;

namespace MMO.Shared.Models.Characters
{
    /// <summary>
    /// Represents a player character in the game
    /// </summary>
    public class Player
    {
        public string PlayerId { get; set; } = string.Empty;
        public string CharacterName { get; set; } = string.Empty;
        public string UserId { get; set; } = string.Empty; // Cognito User ID
        
        // Basic Info
        public int Level { get; set; } = 1;
        public long ExperiencePoints { get; set; } = 0;
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime LastLoginAt { get; set; } = DateTime.UtcNow;
        
        // Position and Zone
        public Vector3 Position { get; set; } = Vector3.Zero;
        public Vector3 Rotation { get; set; } = Vector3.Zero;
        public string CurrentZoneId { get; set; } = string.Empty;
        public string LastKnownZoneId { get; set; } = string.Empty;
        
        // Core Attributes
        public PlayerAttributes Attributes { get; set; } = new PlayerAttributes();
        
        // Health and Status
        public float CurrentHealth { get; set; } = GameConstants.Combat.BaseHealthPoints;
        public float MaxHealth { get; set; } = GameConstants.Combat.BaseHealthPoints;
        public bool IsAlive { get; set; } = true;
        public PlayerStatus Status { get; set; } = PlayerStatus.Online;
        
        // Cybernetics
        public float CurrentSystemPower { get; set; } = GameConstants.Cybernetics.BaseSystemPower;
        public float MaxSystemPower { get; set; } = GameConstants.Cybernetics.BaseSystemPower;
        public float CurrentHumanity { get; set; } = GameConstants.Cybernetics.BaseHumanity;
        public float MaxHumanity { get; set; } = GameConstants.Cybernetics.BaseHumanity;
        
        // Skills
        public Dictionary<string, int> Skills { get; set; } = new Dictionary<string, int>();
        public int AvailableSkillPoints { get; set; } = GameConstants.Character.StartingSkillPoints;
        
        // Inventory and Equipment
        public List<string> InventoryItemIds { get; set; } = new List<string>();
        public Dictionary<EquipmentSlot, string> EquippedItems { get; set; } = new Dictionary<EquipmentSlot, string>();
        
        // Faction Standing
        public Dictionary<string, FactionReputation> FactionStandings { get; set; } = new Dictionary<string, FactionReputation>();
        
        // Quests
        public List<string> ActiveQuestIds { get; set; } = new List<string>();
        public List<string> CompletedQuestIds { get; set; } = new List<string>();
        
        // Crafting
        public List<string> KnownSchematicIds { get; set; } = new List<string>();
        
        // Currency
        public decimal Credits { get; set; } = 1000m; // Starting credits
        
        // Status Effects
        public List<StatusEffect> ActiveStatusEffects { get; set; } = new List<StatusEffect>();
        
        // Guild
        public string GuildId { get; set; } = string.Empty;
        public GuildRank GuildRank { get; set; } = GuildRank.None;
        
        // Housing
        public List<string> OwnedHouseIds { get; set; } = new List<string>();
        
        // Statistics
        public PlayerStatistics Statistics { get; set; } = new PlayerStatistics();
        
        // Calculated Properties
        public float MoveSpeed => GameConstants.Combat.BaseMoveSpeed * (1f + (Attributes.Agility - 10) * 0.1f);
        public float CarryCapacity => 50f + (Attributes.Strength * 5f);
        public bool IsInCombat => ActiveStatusEffects.Exists(e => e.Type == StatusEffectType.InCombat);
        public bool HasCyberPsychosis => CurrentHumanity < GameConstants.Cybernetics.HumanityLossThreshold;
        
        /// <summary>
        /// Calculates the player's total armor rating
        /// </summary>
        public float GetTotalArmorRating()
        {
            // This would be calculated based on equipped armor items
            // For now, return a base value
            return 10f + (Attributes.Constitution * 2f);
        }
        
        /// <summary>
        /// Checks if the player has a specific skill at the required level
        /// </summary>
        public bool HasSkill(string skillName, int requiredLevel)
        {
            return Skills.ContainsKey(skillName) && Skills[skillName] >= requiredLevel;
        }
        
        /// <summary>
        /// Adds experience points and handles level progression
        /// </summary>
        public bool AddExperience(long amount)
        {
            ExperiencePoints += amount;
            
            // Simple level calculation (can be made more complex)
            int newLevel = Math.Min((int)(ExperiencePoints / 1000) + 1, GameConstants.Character.MaxLevel);
            
            if (newLevel > Level)
            {
                Level = newLevel;
                AvailableSkillPoints += 2; // Gain skill points on level up
                return true; // Level up occurred
            }
            
            return false; // No level up
        }
        
        /// <summary>
        /// Applies damage to the player
        /// </summary>
        public bool TakeDamage(float damage)
        {
            CurrentHealth = Math.Max(0, CurrentHealth - damage);
            
            if (CurrentHealth <= 0 && IsAlive)
            {
                IsAlive = false;
                Status = PlayerStatus.Dead;
                return true; // Player died
            }
            
            return false; // Player survived
        }
        
        /// <summary>
        /// Heals the player
        /// </summary>
        public void Heal(float amount)
        {
            CurrentHealth = Math.Min(MaxHealth, CurrentHealth + amount);
            
            if (CurrentHealth > 0 && !IsAlive)
            {
                IsAlive = true;
                Status = PlayerStatus.Online;
            }
        }
    }
    
    /// <summary>
    /// Player core attributes
    /// </summary>
    public class PlayerAttributes
    {
        public int Strength { get; set; } = 10;
        public int Agility { get; set; } = 10;
        public int Intelligence { get; set; } = 10;
        public int Constitution { get; set; } = 10;
        public int Perception { get; set; } = 10;
        public int Charisma { get; set; } = 10;
        public int Technical { get; set; } = 10;
        
        public int TotalPoints => Strength + Agility + Intelligence + Constitution + Perception + Charisma + Technical;
    }
    
    /// <summary>
    /// Player statistics for tracking achievements and progress
    /// </summary>
    public class PlayerStatistics
    {
        public int EnemiesKilled { get; set; } = 0;
        public int PlayersKilled { get; set; } = 0;
        public int Deaths { get; set; } = 0;
        public int QuestsCompleted { get; set; } = 0;
        public int ItemsCrafted { get; set; } = 0;
        public decimal TotalCreditsEarned { get; set; } = 0;
        public decimal TotalCreditsSpent { get; set; } = 0;
        public TimeSpan TotalPlayTime { get; set; } = TimeSpan.Zero;
        public DateTime FirstLogin { get; set; } = DateTime.UtcNow;
    }
    
    public enum PlayerStatus
    {
        Online,
        Offline,
        Away,
        InCombat,
        Dead,
        Unconscious
    }
    
    public enum EquipmentSlot
    {
        Head,
        Torso,
        Arms,
        Legs,
        Feet,
        PrimaryWeapon,
        SecondaryWeapon,
        Utility1,
        Utility2,
        Utility3
    }
    

}
