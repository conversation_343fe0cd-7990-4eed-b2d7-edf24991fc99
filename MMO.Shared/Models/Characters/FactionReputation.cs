using System;

namespace MMO.Shared.Models.Characters
{
    /// <summary>
    /// Represents a player's reputation with a specific faction
    /// </summary>
    public class FactionReputation
    {
        public string FactionId { get; set; } = string.Empty;
        public string FactionName { get; set; } = string.Empty;
        public int ReputationPoints { get; set; } = 0;
        public ReputationLevel Level { get; set; } = ReputationLevel.Neutral;
        public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
        
        /// <summary>
        /// Updates reputation and recalculates the level
        /// </summary>
        public void UpdateReputation(int change)
        {
            ReputationPoints += change;
            Level = CalculateReputationLevel(ReputationPoints);
            LastUpdated = DateTime.UtcNow;
        }
        
        /// <summary>
        /// Calculates reputation level based on points
        /// </summary>
        private static ReputationLevel CalculateReputationLevel(int points)
        {
            return points switch
            {
                >= 10000 => ReputationLevel.Legendary,
                >= 5000 => ReputationLevel.Revered,
                >= 2500 => ReputationLevel.Honored,
                >= 1000 => ReputationLevel.Friendly,
                >= 100 => ReputationLevel.Liked,
                > -100 => ReputationLevel.Neutral,
                >= -1000 => ReputationLevel.Disliked,
                >= -2500 => ReputationLevel.Hostile,
                >= -5000 => ReputationLevel.Hated,
                _ => ReputationLevel.Nemesis
            };
        }
        
        /// <summary>
        /// Gets the reputation level name as a string
        /// </summary>
        public string GetLevelName()
        {
            return Level switch
            {
                ReputationLevel.Legendary => "Legendary",
                ReputationLevel.Revered => "Revered",
                ReputationLevel.Honored => "Honored",
                ReputationLevel.Friendly => "Friendly",
                ReputationLevel.Liked => "Liked",
                ReputationLevel.Neutral => "Neutral",
                ReputationLevel.Disliked => "Disliked",
                ReputationLevel.Hostile => "Hostile",
                ReputationLevel.Hated => "Hated",
                ReputationLevel.Nemesis => "Nemesis",
                _ => "Unknown"
            };
        }
        
        /// <summary>
        /// Checks if the player can access faction-specific content
        /// </summary>
        public bool CanAccessFactionContent(ReputationLevel requiredLevel)
        {
            return Level >= requiredLevel;
        }
    }
    
    public enum ReputationLevel
    {
        Nemesis = -4,
        Hated = -3,
        Hostile = -2,
        Disliked = -1,
        Neutral = 0,
        Liked = 1,
        Friendly = 2,
        Honored = 3,
        Revered = 4,
        Legendary = 5
    }
}
