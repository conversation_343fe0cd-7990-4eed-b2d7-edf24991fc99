using System;

namespace MMO.Shared.Models.Characters
{
    /// <summary>
    /// Represents a temporary status effect applied to a character
    /// </summary>
    public class StatusEffect
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public StatusEffectType Type { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        
        // Duration
        public DateTime StartTime { get; set; } = DateTime.UtcNow;
        public TimeSpan Duration { get; set; } = TimeSpan.Zero;
        public bool IsPermanent { get; set; } = false;
        
        // Effect Values
        public float HealthModifier { get; set; } = 0f;
        public float SystemPowerModifier { get; set; } = 0f;
        public float MoveSpeedModifier { get; set; } = 0f;
        public float DamageModifier { get; set; } = 0f;
        public float ArmorModifier { get; set; } = 0f;
        
        // Stacking
        public bool CanStack { get; set; } = false;
        public int StackCount { get; set; } = 1;
        public int MaxStacks { get; set; } = 1;
        
        // Source
        public string SourceId { get; set; } = string.Empty; // ID of the source (item, ability, etc.)
        public string CasterId { get; set; } = string.Empty; // ID of the player who applied this effect
        
        /// <summary>
        /// Checks if the status effect has expired
        /// </summary>
        public bool HasExpired => !IsPermanent && DateTime.UtcNow >= StartTime.Add(Duration);
        
        /// <summary>
        /// Gets the remaining time for this effect
        /// </summary>
        public TimeSpan RemainingTime
        {
            get
            {
                if (IsPermanent) return TimeSpan.MaxValue;
                var remaining = StartTime.Add(Duration) - DateTime.UtcNow;
                return remaining > TimeSpan.Zero ? remaining : TimeSpan.Zero;
            }
        }
        
        /// <summary>
        /// Refreshes the duration of the status effect
        /// </summary>
        public void Refresh(TimeSpan newDuration)
        {
            StartTime = DateTime.UtcNow;
            Duration = newDuration;
        }
        
        /// <summary>
        /// Adds a stack to this effect if stacking is allowed
        /// </summary>
        public bool AddStack()
        {
            if (!CanStack || StackCount >= MaxStacks)
                return false;
                
            StackCount++;
            return true;
        }
        
        /// <summary>
        /// Removes a stack from this effect
        /// </summary>
        public bool RemoveStack()
        {
            if (StackCount <= 1)
                return false;
                
            StackCount--;
            return true;
        }
        
        /// <summary>
        /// Gets the total modifier value accounting for stacks
        /// </summary>
        public float GetStackedModifier(float baseModifier)
        {
            return baseModifier * StackCount;
        }
    }
    
    public enum StatusEffectType
    {
        // Beneficial Effects
        Healing,
        Regeneration,
        DamageBoost,
        ArmorBoost,
        SpeedBoost,
        SystemPowerBoost,
        
        // Detrimental Effects
        Poison,
        Bleeding,
        Burning,
        Stunned,
        Slowed,
        Weakened,
        EMPDisruption,
        CyberPsychosis,
        
        // Combat States
        InCombat,
        Unconscious,
        Dead,
        
        // Environmental
        Radiation,
        Hypothermia,
        Hyperthermia,
        
        // Cybernetic Effects
        SystemOverload,
        NeuralFeedback,
        ICEBreaker,
        
        // Crafting Effects
        CraftingBonus,
        RepairBonus,
        
        // Social Effects
        FactionBonus,
        TradeBonus,
        
        // Custom
        Custom
    }
}
