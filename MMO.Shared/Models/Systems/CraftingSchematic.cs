using System.Collections.Generic;

namespace MMO.Shared.Models.Systems
{
    /// <summary>
    /// Represents a crafting recipe/schematic
    /// </summary>
    public class CraftingSchematic
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        
        // Requirements
        public Dictionary<string, int> SkillRequirements { get; set; } = new Dictionary<string, int>();
        public int LevelRequirement { get; set; } = 1;
        
        // Components needed
        public Dictionary<string, int> RequiredComponents { get; set; } = new Dictionary<string, int>();
        
        // Output
        public string OutputItemId { get; set; } = string.Empty;
        public int OutputQuantity { get; set; } = 1;
        public float SuccessChance { get; set; } = 0.8f;
        
        // Crafting properties
        public float CraftingTime { get; set; } = 60f; // seconds
        public bool RequiresWorkbench { get; set; } = false;
        public string RequiredTool { get; set; } = string.Empty;
        
        // Rarity and availability
        public bool IsRare { get; set; } = false;
        public string Source { get; set; } = string.Empty; // How to obtain this schematic
    }
}
