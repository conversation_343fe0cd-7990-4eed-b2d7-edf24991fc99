using System;
using System.Collections.Generic;

namespace MMO.Shared.Models.Systems
{
    /// <summary>
    /// Represents a player guild
    /// </summary>
    public class Guild
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string LeaderId { get; set; } = string.Empty;
        public string LeaderName { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public decimal Treasury { get; set; } = 0;
        public int Level { get; set; } = 1;
        public long ExperiencePoints { get; set; } = 0;
        public string Motto { get; set; } = string.Empty;
        public string Tag { get; set; } = string.Empty; // 3-4 character guild tag
        public GuildType Type { get; set; } = GuildType.General;
        
        // Members
        public List<GuildMember> Members { get; set; } = new List<GuildMember>();
        public int MaxMembers { get; set; } = 50;
        
        // Permissions
        public GuildPermissions Permissions { get; set; } = new GuildPermissions();
        
        // Guild Hall
        public string? GuildHallId { get; set; }
        public string? GuildHallLocation { get; set; }
        
        // Statistics
        public GuildStatistics Statistics { get; set; } = new GuildStatistics();
        
        // Status
        public GuildStatus Status { get; set; } = GuildStatus.Active;
        public DateTime LastActivity { get; set; } = DateTime.UtcNow;
        
        /// <summary>
        /// Gets the current member count
        /// </summary>
        public int MemberCount => Members.Count;
        
        /// <summary>
        /// Checks if the guild has space for new members
        /// </summary>
        public bool HasSpace => MemberCount < MaxMembers;
        
        /// <summary>
        /// Gets a member by player ID
        /// </summary>
        public GuildMember? GetMember(string playerId)
        {
            return Members.FirstOrDefault(m => m.PlayerId == playerId);
        }
        
        /// <summary>
        /// Checks if a player has a specific permission
        /// </summary>
        public bool HasPermission(string playerId, GuildPermission permission)
        {
            var member = GetMember(playerId);
            if (member == null) return false;
            
            return member.Rank switch
            {
                GuildRank.Leader => true,
                GuildRank.Officer => Permissions.OfficerPermissions.HasFlag(permission),
                GuildRank.Member => Permissions.MemberPermissions.HasFlag(permission),
                _ => false
            };
        }
    }
    
    /// <summary>
    /// Represents a guild member
    /// </summary>
    public class GuildMember
    {
        public string PlayerId { get; set; } = string.Empty;
        public string PlayerName { get; set; } = string.Empty;
        public GuildRank Rank { get; set; } = GuildRank.Member;
        public DateTime JoinedAt { get; set; } = DateTime.UtcNow;
        public DateTime LastOnline { get; set; } = DateTime.UtcNow;
        public long ContributedExperience { get; set; } = 0;
        public decimal ContributedCredits { get; set; } = 0;
        public string Note { get; set; } = string.Empty;
        public bool IsOnline { get; set; } = false;
    }
    
    /// <summary>
    /// Guild permission settings
    /// </summary>
    public class GuildPermissions
    {
        public GuildPermission OfficerPermissions { get; set; } = 
            GuildPermission.InviteMembers | 
            GuildPermission.KickMembers | 
            GuildPermission.ManageRanks |
            GuildPermission.AccessTreasury |
            GuildPermission.ManageGuildHall;
            
        public GuildPermission MemberPermissions { get; set; } = 
            GuildPermission.AccessGuildHall |
            GuildPermission.UseGuildChat;
    }
    
    /// <summary>
    /// Guild statistics
    /// </summary>
    public class GuildStatistics
    {
        public int TotalMembersEver { get; set; } = 0;
        public int QuestsCompleted { get; set; } = 0;
        public int PvPWins { get; set; } = 0;
        public int PvPLosses { get; set; } = 0;
        public decimal TotalCreditsEarned { get; set; } = 0;
        public long TotalExperienceEarned { get; set; } = 0;
        public DateTime LastRaid { get; set; } = DateTime.MinValue;
        public int RaidsCompleted { get; set; } = 0;
    }
    
    public enum GuildType
    {
        General,
        Military,
        Trading,
        Crafting,
        PvP,
        PvE,
        Social,
        Criminal
    }
    
    public enum GuildStatus
    {
        Active,
        Inactive,
        Disbanded,
        Suspended
    }
    
    public enum GuildRank
    {
        None = 0,
        Member = 1,
        Officer = 2,
        Leader = 3
    }

    [Flags]
    public enum GuildPermission
    {
        None = 0,
        InviteMembers = 1,
        KickMembers = 2,
        ManageRanks = 4,
        AccessTreasury = 8,
        WithdrawFromTreasury = 16,
        ManageGuildHall = 32,
        AccessGuildHall = 64,
        UseGuildChat = 128,
        EditGuildInfo = 256,
        DeclareWar = 512,
        ManageAlliances = 1024
    }
}
