using System;
using System.Collections.Generic;

namespace MMO.Shared.Models.Systems
{
    /// <summary>
    /// Represents a quest in the game
    /// </summary>
    public class Quest
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public QuestType Type { get; set; }
        public QuestStatus Status { get; set; } = QuestStatus.Available;
        
        // Prerequisites
        public List<string> PrerequisiteQuestIds { get; set; } = new List<string>();
        public int LevelRequirement { get; set; } = 1;
        public Dictionary<string, int> SkillRequirements { get; set; } = new Dictionary<string, int>();
        
        // Objectives
        public List<QuestObjective> Objectives { get; set; } = new List<QuestObjective>();
        
        // Rewards
        public long ExperienceReward { get; set; } = 0;
        public decimal CreditReward { get; set; } = 0;
        public List<string> ItemRewards { get; set; } = new List<string>();
        public Dictionary<string, int> ReputationRewards { get; set; } = new Dictionary<string, int>();
        
        // Timing
        public DateTime? TimeLimit { get; set; }
        public DateTime AcceptedAt { get; set; }
        public DateTime? CompletedAt { get; set; }
        
        // Giver and location
        public string QuestGiverId { get; set; } = string.Empty;
        public string StartLocation { get; set; } = string.Empty;
        
        /// <summary>
        /// Checks if all objectives are completed
        /// </summary>
        public bool AreAllObjectivesCompleted()
        {
            return Objectives.TrueForAll(obj => obj.IsCompleted);
        }
        
        /// <summary>
        /// Checks if the quest has expired
        /// </summary>
        public bool HasExpired()
        {
            return TimeLimit.HasValue && DateTime.UtcNow > TimeLimit.Value;
        }
    }
    
    public class QuestObjective
    {
        public string Id { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public ObjectiveType Type { get; set; }
        public bool IsCompleted { get; set; } = false;
        public bool IsOptional { get; set; } = false;
        
        // Progress tracking
        public int CurrentProgress { get; set; } = 0;
        public int RequiredProgress { get; set; } = 1;
        
        // Target information
        public string TargetId { get; set; } = string.Empty;
        public string TargetLocation { get; set; } = string.Empty;
        
        /// <summary>
        /// Updates progress towards the objective
        /// </summary>
        public bool UpdateProgress(int amount = 1)
        {
            if (IsCompleted) return false;
            
            CurrentProgress = Math.Min(CurrentProgress + amount, RequiredProgress);
            
            if (CurrentProgress >= RequiredProgress)
            {
                IsCompleted = true;
                return true;
            }
            
            return false;
        }
    }
    
    public enum QuestType
    {
        Main,
        Side,
        Daily,
        Weekly,
        Guild,
        Faction,
        Personal,
        Event
    }
    
    public enum QuestStatus
    {
        Available,
        Active,
        Completed,
        Failed,
        Abandoned,
        Locked
    }
    
    public enum ObjectiveType
    {
        Kill,
        Collect,
        Deliver,
        Talk,
        Reach,
        Craft,
        Use,
        Survive,
        Escort,
        Hack
    }
}
