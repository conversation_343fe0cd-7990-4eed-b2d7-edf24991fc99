using System;
using System.Collections.Generic;

namespace MMO.Shared.Models.Systems
{
    /// <summary>
    /// Represents a player-owned house
    /// </summary>
    public class PlayerHouse
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string OwnerId { get; set; } = string.Empty;
        public string OwnerName { get; set; } = string.Empty;
        public string Name { get; set; } = "My House";
        public HouseType Type { get; set; } = HouseType.Apartment;
        public string Location { get; set; } = string.Empty;
        public DateTime PurchasedAt { get; set; } = DateTime.UtcNow;
        public DateTime LastMaintenancePaid { get; set; } = DateTime.UtcNow;
        public decimal PurchasePrice { get; set; } = 0;
        public decimal MaintenanceCost { get; set; } = 500;
        
        // House Features
        public int Level { get; set; } = 1;
        public int MaxLevel { get; set; } = 10;
        public List<HouseUpgrade> Upgrades { get; set; } = new List<HouseUpgrade>();
        public int StorageSlots { get; set; } = 50;
        public int MaxStorageSlots { get; set; } = 200;
        
        // Permissions
        public HousePermissions Permissions { get; set; } = new HousePermissions();
        public List<string> AuthorizedPlayers { get; set; } = new List<string>();
        public List<string> BannedPlayers { get; set; } = new List<string>();
        
        // Storage
        public List<string> StoredItemIds { get; set; } = new List<string>();
        
        // Status
        public HouseStatus Status { get; set; } = HouseStatus.Active;
        public DateTime? CondemnationDate { get; set; }
        
        /// <summary>
        /// Checks if maintenance is due
        /// </summary>
        public bool IsMaintenanceDue => DateTime.UtcNow - LastMaintenancePaid > TimeSpan.FromDays(7);
        
        /// <summary>
        /// Checks if the house is condemned
        /// </summary>
        public bool IsCondemned => Status == HouseStatus.Condemned;
        
        /// <summary>
        /// Gets the total maintenance owed
        /// </summary>
        public decimal MaintenanceOwed
        {
            get
            {
                if (!IsMaintenanceDue) return 0;
                
                var weeksPastDue = (DateTime.UtcNow - LastMaintenancePaid).Days / 7;
                return MaintenanceCost * weeksPastDue;
            }
        }
        
        /// <summary>
        /// Checks if a player can access this house
        /// </summary>
        public bool CanPlayerAccess(string playerId)
        {
            if (playerId == OwnerId) return true;
            if (BannedPlayers.Contains(playerId)) return false;
            
            return Permissions.AccessLevel switch
            {
                HouseAccessLevel.Private => AuthorizedPlayers.Contains(playerId),
                HouseAccessLevel.Friends => AuthorizedPlayers.Contains(playerId),
                HouseAccessLevel.Guild => true, // Would check guild membership
                HouseAccessLevel.Public => true,
                _ => false
            };
        }
        
        /// <summary>
        /// Gets the current storage usage
        /// </summary>
        public float StorageUsagePercentage => StorageSlots > 0 ? (float)StoredItemIds.Count / StorageSlots * 100f : 0f;
    }
    
    /// <summary>
    /// Represents a house upgrade
    /// </summary>
    public class HouseUpgrade
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public HouseUpgradeType Type { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public decimal Cost { get; set; } = 0;
        public DateTime InstalledAt { get; set; } = DateTime.UtcNow;
        public bool IsActive { get; set; } = true;
        
        // Effects
        public int StorageBonus { get; set; } = 0;
        public decimal MaintenanceReduction { get; set; } = 0;
        public Dictionary<string, float> Bonuses { get; set; } = new Dictionary<string, float>();
    }
    
    /// <summary>
    /// House permission settings
    /// </summary>
    public class HousePermissions
    {
        public HouseAccessLevel AccessLevel { get; set; } = HouseAccessLevel.Private;
        public bool AllowStorage { get; set; } = false;
        public bool AllowCrafting { get; set; } = false;
        public bool AllowDecorating { get; set; } = false;
        public bool AllowInviting { get; set; } = false;
        public string WelcomeMessage { get; set; } = "Welcome to my home!";
    }
    
    public enum HouseType
    {
        Apartment,
        House,
        Mansion,
        Penthouse,
        Warehouse,
        Workshop,
        Hideout,
        Fortress
    }
    
    public enum HouseStatus
    {
        Active,
        MaintenanceDue,
        Condemned,
        ForSale,
        Abandoned
    }
    
    public enum HouseAccessLevel
    {
        Private,    // Owner only
        Friends,    // Owner + authorized players
        Guild,      // Owner + guild members
        Public      // Anyone can enter
    }
    
    public enum HouseUpgradeType
    {
        Storage,
        Security,
        Crafting,
        Comfort,
        Utility,
        Defense,
        Automation,
        Decoration
    }
}
