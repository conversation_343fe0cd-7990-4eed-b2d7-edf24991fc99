using System;
using System.Collections.Generic;
using MMO.Shared.Models.Items;
using MMO.Shared.Models.Characters;
using MMO.Shared.Models.Common;

namespace MMO.Shared.Models.Systems
{
    /// <summary>
    /// Represents a cybernetic implant that can be installed in a character
    /// </summary>
    public class CyberneticImplant : Item
    {
        public ImplantType ImplantType { get; set; }
        public ImplantSlot Slot { get; set; }
        public ImplantGrade Grade { get; set; } = ImplantGrade.Street;
        
        // System Requirements
        public float SystemPowerDrain { get; set; } = 10f; // Per second when active
        public float HumanityCost { get; set; } = 5f; // Permanent humanity loss
        public float InstallationDifficulty { get; set; } = 50f; // Surgery skill requirement
        
        // Capabilities
        public bool IsActive { get; set; } = false;
        public bool CanToggle { get; set; } = true;
        public float ActivationTime { get; set; } = 1f; // seconds
        public TimeSpan Cooldown { get; set; } = TimeSpan.Zero;
        public DateTime LastActivated { get; set; } = DateTime.MinValue;
        
        // Effects
        public Dictionary<string, float> AttributeModifiers { get; set; } = new Dictionary<string, float>();
        public List<string> GrantedAbilities { get; set; } = new List<string>();
        public List<StatusEffect> PassiveEffects { get; set; } = new List<StatusEffect>();
        
        // EMP Vulnerability
        public float EMPVulnerability { get; set; } = 1f; // Multiplier for EMP damage
        public bool IsEMPHardened { get; set; } = false;
        
        // Rejection Risk
        public float RejectionChance { get; set; } = 0.01f; // 1% base chance per day
        public bool IsRejected { get; set; } = false;
        
        public CyberneticImplant()
        {
            Type = ItemType.CyberneticImplant;
            MaxModSlots = 2;
            IsTradeable = false; // Implants are usually not tradeable once installed
        }
        
        /// <summary>
        /// Activates the implant if possible
        /// </summary>
        public ImplantActivationResult Activate(float availableSystemPower)
        {
            if (IsRejected)
                return new ImplantActivationResult { Success = false, Reason = "Implant rejected" };
                
            if (IsActive)
                return new ImplantActivationResult { Success = false, Reason = "Already active" };
                
            if (!CanToggle)
                return new ImplantActivationResult { Success = false, Reason = "Cannot be toggled" };
                
            if (DateTime.UtcNow - LastActivated < Cooldown)
                return new ImplantActivationResult { Success = false, Reason = "On cooldown" };
                
            if (availableSystemPower < SystemPowerDrain)
                return new ImplantActivationResult { Success = false, Reason = "Insufficient system power" };
            
            IsActive = true;
            LastActivated = DateTime.UtcNow;
            
            return new ImplantActivationResult
            {
                Success = true,
                SystemPowerCost = SystemPowerDrain,
                ActivationTime = ActivationTime
            };
        }
        
        /// <summary>
        /// Deactivates the implant
        /// </summary>
        public bool Deactivate()
        {
            if (!IsActive || !CanToggle) return false;
            
            IsActive = false;
            return true;
        }
        
        /// <summary>
        /// Processes EMP damage to the implant
        /// </summary>
        public EMPDamageResult ProcessEMPDamage(float empDamage)
        {
            if (IsEMPHardened)
                empDamage *= 0.5f; // 50% reduction for hardened implants
            
            float actualDamage = empDamage * EMPVulnerability;
            bool wasDisabled = false;
            
            if (actualDamage > 20f && IsActive)
            {
                IsActive = false;
                wasDisabled = true;
            }
            
            // Apply durability damage
            TakeDamage(actualDamage * 0.1f);
            
            return new EMPDamageResult
            {
                Damage = actualDamage,
                WasDisabled = wasDisabled,
                DurabilityLoss = actualDamage * 0.1f
            };
        }
        
        /// <summary>
        /// Checks for rejection (should be called periodically)
        /// </summary>
        public bool CheckRejection()
        {
            if (IsRejected) return true;
            
            float adjustedChance = RejectionChance;
            
            // Higher grade implants have lower rejection chance
            adjustedChance *= Grade switch
            {
                ImplantGrade.Street => 2f,
                ImplantGrade.Corporate => 1f,
                ImplantGrade.Military => 0.5f,
                ImplantGrade.Prototype => 0.1f,
                _ => 1f
            };
            
            if (new Random().NextSingle() < adjustedChance)
            {
                IsRejected = true;
                IsActive = false;
                return true;
            }
            
            return false;
        }
    }
    
    public class ImplantActivationResult
    {
        public bool Success { get; set; }
        public string Reason { get; set; } = string.Empty;
        public float SystemPowerCost { get; set; }
        public float ActivationTime { get; set; }
    }
    
    public class EMPDamageResult
    {
        public float Damage { get; set; }
        public bool WasDisabled { get; set; }
        public float DurabilityLoss { get; set; }
    }
    
    public enum ImplantType
    {
        // Combat
        ReflexBooster,
        TargetingSystem,
        ArmorPlating,
        WeaponLink,
        
        // Sensory
        CyberEyes,
        CyberEars,
        ThermalVision,
        NightVision,
        
        // Neural
        MemoryChip,
        SkillChip,
        LanguageProcessor,
        DataJack,
        
        // Physical
        CyberArm,
        CyberLeg,
        SpinalColumn,
        HeartPump,
        
        // Utility
        CommLink,
        GPS,
        Scanner,
        Recorder,
        
        // Netrunning
        CyberDeck,
        ICEBreaker,
        Firewall,
        Tracer
    }
    
    public enum ImplantSlot
    {
        Head,
        Eyes,
        Ears,
        Brain,
        Spine,
        Heart,
        Lungs,
        LeftArm,
        RightArm,
        LeftLeg,
        RightLeg,
        Torso,
        Skin,
        Internal
    }
    
    public enum ImplantGrade
    {
        Street,
        Corporate,
        Military,
        Prototype
    }
}
