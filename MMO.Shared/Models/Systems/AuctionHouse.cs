using System;
using System.Collections.Generic;

namespace MMO.Shared.Models.Systems
{
    /// <summary>
    /// Represents an auction house listing
    /// </summary>
    public class AuctionListing
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string SellerId { get; set; } = string.Empty;
        public string SellerName { get; set; } = string.Empty;
        public string ItemId { get; set; } = string.Empty;
        public string ItemName { get; set; } = string.Empty;
        public string ItemDescription { get; set; } = string.Empty;
        public decimal Price { get; set; }
        public int Quantity { get; set; } = 1;
        public string Location { get; set; } = string.Empty; // Where item is located
        public DateTime ListedAt { get; set; } = DateTime.UtcNow;
        public DateTime ExpiresAt { get; set; }
        public AuctionStatus Status { get; set; } = AuctionStatus.Active;
        public List<string> Tags { get; set; } = new List<string>();
        
        /// <summary>
        /// Checks if the listing has expired
        /// </summary>
        public bool HasExpired => DateTime.UtcNow > ExpiresAt;
        
        /// <summary>
        /// Gets the time remaining for this listing
        /// </summary>
        public TimeSpan TimeRemaining => ExpiresAt > DateTime.UtcNow ? ExpiresAt - DateTime.UtcNow : TimeSpan.Zero;
    }
    
    /// <summary>
    /// Represents a completed auction transaction
    /// </summary>
    public class AuctionTransaction
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string ListingId { get; set; } = string.Empty;
        public string SellerId { get; set; } = string.Empty;
        public string BuyerId { get; set; } = string.Empty;
        public string ItemId { get; set; } = string.Empty;
        public string ItemName { get; set; } = string.Empty;
        public decimal Price { get; set; }
        public decimal SalesTax { get; set; }
        public decimal ShippingCost { get; set; }
        public decimal TotalCost { get; set; }
        public string PickupLocation { get; set; } = string.Empty;
        public string DeliveryLocation { get; set; } = string.Empty;
        public DateTime TransactionDate { get; set; } = DateTime.UtcNow;
        public DateTime EstimatedDelivery { get; set; }
        public TransactionStatus Status { get; set; } = TransactionStatus.Processing;
    }
    
    /// <summary>
    /// Search criteria for auction house listings
    /// </summary>
    public class AuctionSearchCriteria
    {
        public string? ItemName { get; set; }
        public string? Category { get; set; }
        public decimal? MinPrice { get; set; }
        public decimal? MaxPrice { get; set; }
        public string? Location { get; set; }
        public List<string> Tags { get; set; } = new List<string>();
        public AuctionSortBy SortBy { get; set; } = AuctionSortBy.PriceAscending;
        public int MaxResults { get; set; } = 50;
        public int Skip { get; set; } = 0;
    }
    
    /// <summary>
    /// Represents a trade offer between players
    /// </summary>
    public class TradeOffer
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Player1Id { get; set; } = string.Empty;
        public string Player2Id { get; set; } = string.Empty;
        public List<TradeItem> Player1Items { get; set; } = new List<TradeItem>();
        public List<TradeItem> Player2Items { get; set; } = new List<TradeItem>();
        public decimal Player1Credits { get; set; } = 0;
        public decimal Player2Credits { get; set; } = 0;
        public bool Player1Accepted { get; set; } = false;
        public bool Player2Accepted { get; set; } = false;
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime ExpiresAt { get; set; } = DateTime.UtcNow.AddMinutes(30);
        public TradeStatus Status { get; set; } = TradeStatus.Pending;
        
        /// <summary>
        /// Checks if both players have accepted the trade
        /// </summary>
        public bool IsBothAccepted => Player1Accepted && Player2Accepted;
        
        /// <summary>
        /// Checks if the trade offer has expired
        /// </summary>
        public bool HasExpired => DateTime.UtcNow > ExpiresAt;
    }
    
    /// <summary>
    /// Represents an item in a trade offer
    /// </summary>
    public class TradeItem
    {
        public string ItemId { get; set; } = string.Empty;
        public string ItemName { get; set; } = string.Empty;
        public int Quantity { get; set; } = 1;
        public decimal EstimatedValue { get; set; } = 0;
    }
    
    public enum AuctionStatus
    {
        Active,
        Sold,
        Expired,
        Cancelled
    }
    
    public enum TransactionStatus
    {
        Processing,
        InTransit,
        Delivered,
        Failed,
        Cancelled
    }
    
    public enum AuctionSortBy
    {
        PriceAscending,
        PriceDescending,
        TimeRemaining,
        RecentlyListed,
        ItemName,
        Location
    }
    
    public enum TradeStatus
    {
        Pending,
        Accepted,
        Completed,
        Cancelled,
        Expired
    }
}
