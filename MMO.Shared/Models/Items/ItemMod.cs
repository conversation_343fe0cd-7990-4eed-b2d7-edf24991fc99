using System;
using System.Collections.Generic;

namespace MMO.Shared.Models.Items
{
    /// <summary>
    /// Represents a modification that can be applied to items
    /// </summary>
    public class ItemMod
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public ModType Type { get; set; }
        public ModRarity Rarity { get; set; } = ModRarity.Common;
        
        // Modification Values
        public float DamageModifier { get; set; } = 0f;
        public float ArmorModifier { get; set; } = 0f;
        public float AccuracyModifier { get; set; } = 0f;
        public float RangeModifier { get; set; } = 0f;
        public float FireRateModifier { get; set; } = 0f;
        public float ReloadSpeedModifier { get; set; } = 0f;
        public float CriticalChanceModifier { get; set; } = 0f;
        public float CriticalDamageModifier { get; set; } = 0f;
        public float WeightModifier { get; set; } = 0f;
        public decimal ValueModifier { get; set; } = 0m;
        
        // Special Properties
        public Dictionary<string, float> SpecialProperties { get; set; } = new Dictionary<string, float>();
        
        // Requirements and Restrictions
        public List<ItemType> CompatibleItemTypes { get; set; } = new List<ItemType>();
        public Dictionary<string, int> InstallationRequirements { get; set; } = new Dictionary<string, int>();
        public int LevelRequirement { get; set; } = 1;
        
        // Installation
        public bool IsInstalled { get; set; } = false;
        public string InstalledItemId { get; set; } = string.Empty;
        public DateTime InstallationDate { get; set; } = DateTime.UtcNow;
        public string InstalledBy { get; set; } = string.Empty; // Player ID who installed it
        
        // Durability Impact
        public float DurabilityImpact { get; set; } = 0f; // How much this mod affects item durability loss
        
        /// <summary>
        /// Checks if this mod can be installed on the specified item type
        /// </summary>
        public bool IsCompatibleWith(ItemType itemType)
        {
            return CompatibleItemTypes.Count == 0 || CompatibleItemTypes.Contains(itemType);
        }
        
        /// <summary>
        /// Checks if the player meets the requirements to install this mod
        /// </summary>
        public bool CanBeInstalledBy(Dictionary<string, int> playerSkills, int playerLevel)
        {
            if (playerLevel < LevelRequirement) return false;
            
            foreach (var requirement in InstallationRequirements)
            {
                if (!playerSkills.ContainsKey(requirement.Key) || 
                    playerSkills[requirement.Key] < requirement.Value)
                {
                    return false;
                }
            }
            
            return true;
        }
        
        /// <summary>
        /// Gets a special property value by name
        /// </summary>
        public float GetSpecialProperty(string propertyName)
        {
            return SpecialProperties.ContainsKey(propertyName) ? SpecialProperties[propertyName] : 0f;
        }
        
        /// <summary>
        /// Sets a special property value
        /// </summary>
        public void SetSpecialProperty(string propertyName, float value)
        {
            SpecialProperties[propertyName] = value;
        }
        
        /// <summary>
        /// Gets the total modifier value for display purposes
        /// </summary>
        public string GetModifierSummary()
        {
            var modifiers = new List<string>();
            
            if (DamageModifier != 0)
                modifiers.Add($"Damage: {DamageModifier:+0.0;-0.0}");
            if (ArmorModifier != 0)
                modifiers.Add($"Armor: {ArmorModifier:+0.0;-0.0}");
            if (AccuracyModifier != 0)
                modifiers.Add($"Accuracy: {AccuracyModifier:+0.0%;-0.0%}");
            if (RangeModifier != 0)
                modifiers.Add($"Range: {RangeModifier:+0.0;-0.0}m");
            if (FireRateModifier != 0)
                modifiers.Add($"Fire Rate: {FireRateModifier:+0.0%;-0.0%}");
            if (ReloadSpeedModifier != 0)
                modifiers.Add($"Reload Speed: {ReloadSpeedModifier:+0.0%;-0.0%}");
            if (CriticalChanceModifier != 0)
                modifiers.Add($"Crit Chance: {CriticalChanceModifier:+0.0%;-0.0%}");
            if (CriticalDamageModifier != 0)
                modifiers.Add($"Crit Damage: {CriticalDamageModifier:+0.0%;-0.0%}");
            if (WeightModifier != 0)
                modifiers.Add($"Weight: {WeightModifier:+0.0;-0.0}kg");
            
            return string.Join(", ", modifiers);
        }
    }
    
    public enum ModType
    {
        // Weapon Mods
        Scope,
        Barrel,
        Stock,
        Grip,
        Magazine,
        Trigger,
        Muzzle,
        
        // Armor Mods
        Plating,
        Padding,
        Servos,
        PowerCell,
        Cooling,
        
        // Cybernetic Mods
        Processor,
        Memory,
        Interface,
        Amplifier,
        
        // Utility Mods
        Enhancement,
        Optimization,
        Reinforcement,
        
        // Special
        Experimental,
        Prototype,
        Custom
    }
    
    public enum ModRarity
    {
        Common,
        Uncommon,
        Rare,
        Epic,
        Legendary,
        Prototype
    }
}
