using System;
using System.Collections.Generic;
using MMO.Shared.Models.Common;

namespace MMO.Shared.Models.Items
{
    /// <summary>
    /// Base class for all items in the game
    /// </summary>
    public abstract class Item
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string TemplateId { get; set; } = string.Empty; // Reference to item template
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public ItemType Type { get; set; }
        public ItemRarity Rarity { get; set; } = ItemRarity.Common;
        
        // Physical Properties
        public float Weight { get; set; } = 0f;
        public int StackSize { get; set; } = 1;
        public int CurrentStack { get; set; } = 1;
        
        // Durability
        public float MaxDurability { get; set; } = GameConstants.Crafting.MaxDurability;
        public float CurrentDurability { get; set; } = GameConstants.Crafting.MaxDurability;
        public bool CanRepair { get; set; } = true;
        
        // Value
        public decimal BaseValue { get; set; } = 0m;
        public decimal CurrentValue => CalculateCurrentValue();
        
        // Ownership
        public string OwnerId { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime LastModified { get; set; } = DateTime.UtcNow;
        
        // Modifications
        public List<ItemMod> Modifications { get; set; } = new List<ItemMod>();
        public int MaxModSlots { get; set; } = 0;
        
        // Requirements
        public Dictionary<string, int> SkillRequirements { get; set; } = new Dictionary<string, int>();
        public int LevelRequirement { get; set; } = 1;
        
        // Flags
        public bool IsEquipped { get; set; } = false;
        public bool IsTradeable { get; set; } = true;
        public bool IsDroppable { get; set; } = true;
        public bool IsDestroyable { get; set; } = true;
        public bool IsUnique { get; set; } = false;
        
        /// <summary>
        /// Calculates the current value based on durability and modifications
        /// </summary>
        protected virtual decimal CalculateCurrentValue()
        {
            float durabilityMultiplier = CurrentDurability / MaxDurability;
            decimal modValue = 0m;
            
            foreach (var mod in Modifications)
            {
                modValue += mod.ValueModifier;
            }
            
            return (BaseValue + modValue) * (decimal)durabilityMultiplier;
        }
        
        /// <summary>
        /// Damages the item, reducing its durability
        /// </summary>
        public virtual bool TakeDamage(float damage)
        {
            if (CurrentDurability <= 0) return true; // Already broken
            
            CurrentDurability = Math.Max(0, CurrentDurability - damage);
            LastModified = DateTime.UtcNow;
            
            return CurrentDurability <= 0; // Returns true if item is now broken
        }
        
        /// <summary>
        /// Repairs the item
        /// </summary>
        public virtual bool Repair(float amount, float efficiency = 1f)
        {
            if (!CanRepair || CurrentDurability >= MaxDurability) return false;
            
            float actualRepair = amount * efficiency;
            CurrentDurability = Math.Min(MaxDurability, CurrentDurability + actualRepair);
            LastModified = DateTime.UtcNow;
            
            return true;
        }
        
        /// <summary>
        /// Adds a modification to the item
        /// </summary>
        public virtual bool AddModification(ItemMod mod)
        {
            if (Modifications.Count >= MaxModSlots) return false;
            
            Modifications.Add(mod);
            LastModified = DateTime.UtcNow;
            return true;
        }
        
        /// <summary>
        /// Removes a modification from the item
        /// </summary>
        public virtual bool RemoveModification(string modId)
        {
            var mod = Modifications.Find(m => m.Id == modId);
            if (mod == null) return false;
            
            Modifications.Remove(mod);
            LastModified = DateTime.UtcNow;
            return true;
        }
        
        /// <summary>
        /// Checks if the item can be used by a player
        /// </summary>
        public virtual bool CanBeUsedBy(string playerId, Dictionary<string, int> playerSkills, int playerLevel)
        {
            if (playerLevel < LevelRequirement) return false;
            
            foreach (var requirement in SkillRequirements)
            {
                if (!playerSkills.ContainsKey(requirement.Key) || 
                    playerSkills[requirement.Key] < requirement.Value)
                {
                    return false;
                }
            }
            
            return true;
        }
        
        /// <summary>
        /// Gets the total weight including modifications
        /// </summary>
        public virtual float GetTotalWeight()
        {
            float totalWeight = Weight * CurrentStack;
            
            foreach (var mod in Modifications)
            {
                totalWeight += mod.WeightModifier;
            }
            
            return totalWeight;
        }
        
        /// <summary>
        /// Checks if the item is broken
        /// </summary>
        public bool IsBroken => CurrentDurability <= 0;
        
        /// <summary>
        /// Gets the durability percentage
        /// </summary>
        public float DurabilityPercentage => MaxDurability > 0 ? (CurrentDurability / MaxDurability) * 100f : 0f;
    }
    
    public enum ItemType
    {
        Weapon,
        Armor,
        Consumable,
        CraftingComponent,
        CyberneticImplant,
        Utility,
        Quest,
        Currency,
        Housing,
        Misc
    }
    
    public enum ItemRarity
    {
        Common,
        Uncommon,
        Rare,
        Epic,
        Legendary,
        Artifact
    }
}
