namespace MMO.Shared.Models.Items
{
    /// <summary>
    /// Represents a crafting component used in item creation
    /// </summary>
    public class CraftingComponent : Item
    {
        public ComponentType ComponentType { get; set; }
        public ComponentGrade Grade { get; set; } = ComponentGrade.Standard;
        public float Purity { get; set; } = 1f; // 0.0 to 1.0, affects crafting quality
        
        // Crafting Properties
        public bool IsRare { get; set; } = false;
        public string SourceLocation { get; set; } = string.Empty;
        public bool RequiresSpecialHandling { get; set; } = false;
        
        public CraftingComponent()
        {
            Type = ItemType.CraftingComponent;
            StackSize = 100;
            MaxModSlots = 0; // Components can't be modded
        }
        
        /// <summary>
        /// Gets the quality modifier for crafting
        /// </summary>
        public float GetQualityModifier()
        {
            float gradeModifier = Grade switch
            {
                ComponentGrade.Scrap => 0.5f,
                ComponentGrade.Standard => 1f,
                ComponentGrade.HighGrade => 1.5f,
                ComponentGrade.Military => 2f,
                ComponentGrade.Prototype => 3f,
                _ => 1f
            };
            
            return gradeModifier * Purity;
        }
    }
    
    public enum ComponentType
    {
        // Electronics
        Processor,
        Memory,
        Capacitor,
        Resistor,
        Transistor,
        Circuit,
        
        // Mechanical
        Gear,
        Spring,
        Bearing,
        Servo,
        Motor,
        Actuator,
        
        // Materials
        Steel,
        Aluminum,
        Titanium,
        Polymer,
        Ceramic,
        Composite,
        
        // Chemical
        Acid,
        Solvent,
        Catalyst,
        Explosive,
        Fuel,
        Coolant,
        
        // Cybernetic
        Bioware,
        Nanotech,
        Quantum,
        Neural,
        Synthetic,
        
        // Energy
        Battery,
        PowerCell,
        Generator,
        Conductor,
        Insulator,
        
        // Rare
        Exotic,
        Alien,
        Experimental
    }
    
    public enum ComponentGrade
    {
        Scrap,
        Standard,
        HighGrade,
        Military,
        Prototype
    }
}
