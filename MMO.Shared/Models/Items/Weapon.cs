using System;
using System.Collections.Generic;
using MMO.Shared.Models.Common;

namespace MMO.Shared.Models.Items
{
    /// <summary>
    /// Represents a weapon item
    /// </summary>
    public class Weapon : Item
    {
        public WeaponType WeaponType { get; set; }
        public DamageType PrimaryDamageType { get; set; }
        public DamageType SecondaryDamageType { get; set; } = DamageType.None;
        
        // Damage Properties
        public float BaseDamage { get; set; } = 10f;
        public float MinDamage { get; set; } = 8f;
        public float MaxDamage { get; set; } = 12f;
        public float ArmorPenetration { get; set; } = 0f;
        public float CriticalChance { get; set; } = 0.05f; // 5%
        public float CriticalDamageMultiplier { get; set; } = GameConstants.Combat.CriticalHitMultiplier;
        
        // Accuracy and Range
        public float BaseAccuracy { get; set; } = 0.8f; // 80%
        public float EffectiveRange { get; set; } = 50f; // meters
        public float MaxRange { get; set; } = 100f; // meters
        public float AccuracyFalloff { get; set; } = 0.1f; // Accuracy loss per meter beyond effective range
        
        // Rate of Fire
        public float FireRate { get; set; } = 1f; // Shots per second
        public FireMode FireMode { get; set; } = FireMode.SemiAutomatic;
        public int BurstSize { get; set; } = 3; // For burst fire weapons
        
        // Ammunition
        public string AmmoType { get; set; } = string.Empty;
        public int MagazineSize { get; set; } = 30;
        public int CurrentAmmo { get; set; } = 30;
        public float ReloadTime { get; set; } = 2f; // seconds
        
        // Recoil and Handling
        public float RecoilPattern { get; set; } = 1f;
        public float RecoilRecovery { get; set; } = 0.5f; // seconds to recover from recoil
        public float AimDownSightTime { get; set; } = 0.3f; // seconds
        public float MovementAccuracyPenalty { get; set; } = 0.2f; // Accuracy reduction while moving
        
        // Special Properties
        public bool HasScope { get; set; } = false;
        public float ScopeZoom { get; set; } = 1f;
        public bool IsSilenced { get; set; } = false;
        public bool HasFullAuto { get; set; } = false;
        public bool IsEnergyWeapon { get; set; } = false;
        public bool IsSmartWeapon { get; set; } = false; // Can use smart ammunition
        
        // Cybernetic Integration
        public bool RequiresCyberLink { get; set; } = false;
        public float SystemPowerDrain { get; set; } = 0f; // Per shot for cyber weapons
        
        public Weapon()
        {
            Type = ItemType.Weapon;
            MaxModSlots = 5; // Weapons can have more mods
        }
        
        /// <summary>
        /// Calculates the effective damage at a given range
        /// </summary>
        public float CalculateDamageAtRange(float range)
        {
            float damage = BaseDamage;
            
            // Apply modifications
            foreach (var mod in Modifications)
            {
                damage += mod.DamageModifier;
            }
            
            // Apply range falloff for non-energy weapons
            if (!IsEnergyWeapon && range > EffectiveRange)
            {
                float falloffDistance = range - EffectiveRange;
                float maxFalloffDistance = MaxRange - EffectiveRange;
                float falloffRatio = Math.Min(falloffDistance / maxFalloffDistance, 1f);
                damage *= (1f - (falloffRatio * 0.5f)); // Max 50% damage reduction
            }
            
            return Math.Max(damage * 0.1f, damage); // Minimum 10% damage
        }
        
        /// <summary>
        /// Calculates the effective accuracy at a given range
        /// </summary>
        public float CalculateAccuracyAtRange(float range, bool isMoving = false)
        {
            float accuracy = BaseAccuracy;
            
            // Apply modifications
            foreach (var mod in Modifications)
            {
                accuracy += mod.AccuracyModifier;
            }
            
            // Apply range penalty
            if (range > EffectiveRange)
            {
                float excessRange = range - EffectiveRange;
                accuracy -= excessRange * AccuracyFalloff;
            }
            
            // Apply movement penalty
            if (isMoving)
            {
                accuracy -= MovementAccuracyPenalty;
            }
            
            // Apply scope bonus if applicable
            if (HasScope)
            {
                accuracy += 0.1f; // 10% accuracy bonus with scope
            }
            
            return Math.Max(0.05f, Math.Min(1f, accuracy)); // Clamp between 5% and 100%
        }
        
        /// <summary>
        /// Fires the weapon and returns damage information
        /// </summary>
        public WeaponFireResult Fire(float range, bool isMoving = false, bool isAiming = false)
        {
            if (CurrentAmmo <= 0)
            {
                return new WeaponFireResult { Success = false, Reason = "No ammunition" };
            }
            
            CurrentAmmo--;
            
            float accuracy = CalculateAccuracyAtRange(range, isMoving);
            if (isAiming && HasScope)
            {
                accuracy += 0.05f; // Additional 5% bonus when aiming with scope
            }
            
            bool hit = new Random().NextSingle() <= accuracy;
            if (!hit)
            {
                return new WeaponFireResult { Success = true, Hit = false };
            }
            
            float damage = CalculateDamageAtRange(range);
            bool isCritical = new Random().NextSingle() <= CriticalChance;
            
            if (isCritical)
            {
                damage *= CriticalDamageMultiplier;
            }
            
            // Apply durability loss
            TakeDamage(GameConstants.Crafting.DurabilityLossPerUse);
            
            return new WeaponFireResult
            {
                Success = true,
                Hit = true,
                Damage = damage,
                DamageType = PrimaryDamageType,
                IsCritical = isCritical,
                ArmorPenetration = ArmorPenetration
            };
        }
        
        /// <summary>
        /// Reloads the weapon
        /// </summary>
        public bool Reload()
        {
            if (CurrentAmmo >= MagazineSize)
                return false;
                
            CurrentAmmo = MagazineSize;
            return true;
        }
        
        /// <summary>
        /// Gets the effective fire rate with modifications
        /// </summary>
        public float GetEffectiveFireRate()
        {
            float rate = FireRate;
            
            foreach (var mod in Modifications)
            {
                rate *= (1f + mod.FireRateModifier);
            }
            
            return rate;
        }
        
        /// <summary>
        /// Gets the effective reload time with modifications
        /// </summary>
        public float GetEffectiveReloadTime()
        {
            float time = ReloadTime;
            
            foreach (var mod in Modifications)
            {
                time *= (1f - mod.ReloadSpeedModifier);
            }
            
            return Math.Max(0.1f, time); // Minimum 0.1 second reload
        }
    }
    
    public class WeaponFireResult
    {
        public bool Success { get; set; }
        public bool Hit { get; set; }
        public float Damage { get; set; }
        public DamageType DamageType { get; set; }
        public bool IsCritical { get; set; }
        public float ArmorPenetration { get; set; }
        public string Reason { get; set; } = string.Empty;
    }
    
    public enum WeaponType
    {
        // Ballistic Weapons
        Pistol,
        Rifle,
        Shotgun,
        SniperRifle,
        SubmachineGun,
        AssaultRifle,
        
        // Energy Weapons
        LaserPistol,
        LaserRifle,
        PlasmaCannon,
        ParticleBeam,
        
        // Melee Weapons
        Knife,
        Sword,
        Club,
        MonoWire,
        CyberBlade,
        
        // Heavy Weapons
        RocketLauncher,
        GrenadeLauncher,
        Minigun,
        RailGun,
        
        // Special
        SmartGun,
        NetrunnerDeck,
        EMPDevice,
        Experimental
    }
    
    public enum DamageType
    {
        None,
        Ballistic,
        Energy,
        EMP,
        Explosive,
        Melee,
        Poison,
        Fire,
        Ice,
        Acid,
        Psychic
    }
    
    public enum FireMode
    {
        SemiAutomatic,
        FullAutomatic,
        Burst,
        Single
    }
}
