using System.Collections.Generic;
using MMO.Shared.Models.Characters;

namespace MMO.Shared.Models.Items
{
    /// <summary>
    /// Represents an armor item
    /// </summary>
    public class Armor : Item
    {
        public ArmorType ArmorType { get; set; }
        public EquipmentSlot Slot { get; set; }
        
        // Protection Values
        public float BallisticProtection { get; set; } = 10f;
        public float EnergyProtection { get; set; } = 5f;
        public float EMPProtection { get; set; } = 0f;
        public float ExplosiveProtection { get; set; } = 5f;
        
        // Mobility Impact
        public float MovementSpeedModifier { get; set; } = 0f; // Negative values slow down
        public float StealthModifier { get; set; } = 0f; // Negative values make more noise
        
        // Environmental Protection
        public float RadiationProtection { get; set; } = 0f;
        public float TemperatureProtection { get; set; } = 0f;
        public float ToxinProtection { get; set; } = 0f;
        
        // Special Features
        public bool HasActiveShielding { get; set; } = false;
        public float ShieldCapacity { get; set; } = 0f;
        public float ShieldRegenRate { get; set; } = 0f;
        public bool RequiresPower { get; set; } = false;
        public float PowerConsumption { get; set; } = 0f; // Per second
        
        public Armor()
        {
            Type = ItemType.Armor;
            MaxModSlots = 3;
        }
        
        /// <summary>
        /// Calculates damage reduction against a specific damage type
        /// </summary>
        public float CalculateDamageReduction(DamageType damageType)
        {
            float protection = damageType switch
            {
                DamageType.Ballistic => BallisticProtection,
                DamageType.Energy => EnergyProtection,
                DamageType.EMP => EMPProtection,
                DamageType.Explosive => ExplosiveProtection,
                _ => 0f
            };
            
            // Apply modifications
            foreach (var mod in Modifications)
            {
                protection += mod.ArmorModifier;
            }
            
            // Apply durability factor
            float durabilityFactor = CurrentDurability / MaxDurability;
            protection *= durabilityFactor;
            
            // Convert to damage reduction percentage (diminishing returns)
            return protection / (protection + 100f);
        }
        
        /// <summary>
        /// Gets the total movement speed modifier including mods
        /// </summary>
        public float GetTotalMovementModifier()
        {
            float modifier = MovementSpeedModifier;
            
            foreach (var mod in Modifications)
            {
                if (mod.SpecialProperties.ContainsKey("MovementSpeed"))
                {
                    modifier += mod.SpecialProperties["MovementSpeed"];
                }
            }
            
            return modifier;
        }
    }
    
    public enum ArmorType
    {
        Light,
        Medium,
        Heavy,
        PowerArmor,
        Cybernetic,
        Environmental,
        Stealth
    }
}
