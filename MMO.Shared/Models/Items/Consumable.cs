using System;
using System.Collections.Generic;
using MMO.Shared.Models.Characters;

namespace MMO.Shared.Models.Items
{
    /// <summary>
    /// Represents a consumable item
    /// </summary>
    public class Consumable : Item
    {
        public ConsumableType ConsumableType { get; set; }
        public float UseTime { get; set; } = 1f; // seconds to consume
        public int MaxUses { get; set; } = 1;
        public int RemainingUses { get; set; } = 1;
        
        // Effects
        public float HealthRestore { get; set; } = 0f;
        public float SystemPowerRestore { get; set; } = 0f;
        public List<StatusEffect> AppliedEffects { get; set; } = new List<StatusEffect>();
        
        // Restrictions
        public bool CanUseInCombat { get; set; } = true;
        public TimeSpan Cooldown { get; set; } = TimeSpan.Zero;
        public DateTime LastUsed { get; set; } = DateTime.MinValue;
        
        public Consumable()
        {
            Type = ItemType.Consumable;
            StackSize = 10;
        }
        
        /// <summary>
        /// Uses the consumable and returns the effects
        /// </summary>
        public ConsumableUseResult Use()
        {
            if (RemainingUses <= 0)
                return new ConsumableUseResult { Success = false, Reason = "No uses remaining" };
                
            if (DateTime.UtcNow - LastUsed < Cooldown)
                return new ConsumableUseResult { Success = false, Reason = "On cooldown" };
            
            RemainingUses--;
            LastUsed = DateTime.UtcNow;
            
            return new ConsumableUseResult
            {
                Success = true,
                HealthRestore = HealthRestore,
                SystemPowerRestore = SystemPowerRestore,
                StatusEffects = new List<StatusEffect>(AppliedEffects)
            };
        }
    }
    
    public class ConsumableUseResult
    {
        public bool Success { get; set; }
        public string Reason { get; set; } = string.Empty;
        public float HealthRestore { get; set; }
        public float SystemPowerRestore { get; set; }
        public List<StatusEffect> StatusEffects { get; set; } = new List<StatusEffect>();
    }
    
    public enum ConsumableType
    {
        HealthPack,
        Stimulant,
        Booster,
        Antidote,
        Food,
        Drink,
        Drug,
        Cybernetic,
        Utility
    }
}
