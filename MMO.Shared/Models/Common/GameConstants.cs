namespace MMO.Shared.Models.Common
{
    /// <summary>
    /// Contains game-wide constants and configuration values
    /// </summary>
    public static class GameConstants
    {
        // Combat Constants
        public static class Combat
        {
            public const float BaseHealthPoints = 100f;
            public const float BaseMoveSpeed = 5f;
            public const float CriticalHitMultiplier = 2f;
            public const float HeadshotMultiplier = 3f;
            
            // Damage type modifiers
            public static class DamageModifiers
            {
                public const float BallisticVsFlesh = 1f;
                public const float BallisticVsArmor = 0.8f;
                public const float BallisticVsCyber = 0.6f;
                
                public const float EnergyVsFlesh = 1.2f;
                public const float EnergyVsArmor = 1.5f;
                public const float EnergyVsCyber = 0.4f;
                
                public const float EMPVsFlesh = 0.1f;
                public const float EMPVsArmor = 0.2f;
                public const float EMPVsCyber = 2f;
            }
            
            // Hit location multipliers
            public static class HitLocationMultipliers
            {
                public const float Head = 3f;
                public const float Torso = 1f;
                public const float Arms = 0.8f;
                public const float Legs = 0.7f;
            }
        }
        
        // Cybernetics Constants
        public static class Cybernetics
        {
            public const float BaseSystemPower = 100f;
            public const float BaseHumanity = 100f;
            public const float SystemPowerRegenRate = 5f; // per second
            public const float HumanityLossThreshold = 50f;
            
            // EMP effects
            public const float EMPDisableDuration = 10f; // seconds
            public const float EMPSystemPowerDrain = 50f;
        }
        
        // Crafting Constants
        public static class Crafting
        {
            public const int MaxCraftingSkill = 100;
            public const int MinCraftingSkill = 0;
            public const float SkillSuccessBaseChance = 0.5f;
            public const float SkillBonusPerLevel = 0.01f;
            
            // Durability
            public const float MaxDurability = 100f;
            public const float DurabilityLossPerUse = 1f;
            public const float RepairEfficiency = 0.8f;
        }
        
        // Economy Constants
        public static class Economy
        {
            public const decimal AuctionHouseListingFee = 0.05m; // 5%
            public const decimal AuctionHouseSalesTax = 0.10m; // 10%
            public const int MaxAuctionDuration = 168; // hours (7 days)
            public const int MinAuctionDuration = 1; // hour
            
            // Shipping times (in hours)
            public const int LocalShippingTime = 1;
            public const int RegionalShippingTime = 6;
            public const int GlobalShippingTime = 24;
        }
        
        // Guild Constants
        public static class Guilds
        {
            public const int MaxGuildMembers = 100;
            public const int MinGuildMembers = 3;
            public const decimal GuildCreationCost = 10000m;
            public const decimal WeeklyUpkeepCost = 1000m;
            public const int MaxGuildName = 50;
            public const int MaxGuildDescription = 500;
        }
        
        // Housing Constants
        public static class Housing
        {
            public const int MaxHousesPerPlayer = 3;
            public const decimal BaseHouseCost = 50000m;
            public const decimal WeeklyMaintenanceCost = 500m;
            public const int MaxHouseUpgrades = 10;
            public const int MaxStorageSlots = 200;
        }
        
        // Networking Constants
        public static class Networking
        {
            public const int DefaultGamePort = 7777;
            public const int MaxPlayersPerZone = 100;
            public const float NetworkUpdateRate = 20f; // Hz
            public const float PlayerPositionUpdateRate = 10f; // Hz
            public const int MaxPacketSize = 1024; // bytes
        }
        
        // Zone Constants
        public static class Zones
        {
            public const float ZoneSize = 1000f; // meters
            public const int HeartbeatInterval = 30; // seconds
            public const int ZoneTimeoutDuration = 120; // seconds
        }
        
        // Character Constants
        public static class Character
        {
            public const int MaxLevel = 50;
            public const int MaxSkillPoints = 100;
            public const int StartingSkillPoints = 10;
            public const int MaxCharacterName = 50;
            public const int MinCharacterName = 3;
            
            // Attribute limits
            public const int MaxAttributeValue = 20;
            public const int MinAttributeValue = 1;
            public const int StartingAttributePoints = 27;
        }
        
        // Item Constants
        public static class Items
        {
            public const int MaxStackSize = 999;
            public const int MaxItemMods = 5;
            public const float MaxItemWeight = 50f; // kg
            public const int MaxInventorySlots = 50;
        }
    }
}
