using System;
using MMO.Shared.Models.Common;
using MMO.Shared.Models.Items;

namespace MMO.Shared.Models.Networking
{
    /// <summary>
    /// Base class for all network packets
    /// </summary>
    public abstract class NetworkPacket
    {
        public string PacketId { get; set; } = Guid.NewGuid().ToString();
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public string SenderId { get; set; } = string.Empty;
        public PacketType Type { get; set; }
    }
    
    /// <summary>
    /// Packet for player movement updates
    /// </summary>
    public class PlayerMovementPacket : NetworkPacket
    {
        public Vector3 Position { get; set; }
        public Vector3 Rotation { get; set; }
        public Vector3 Velocity { get; set; }
        public bool IsGrounded { get; set; }
        public MovementState State { get; set; }
        
        public PlayerMovementPacket()
        {
            Type = PacketType.PlayerMovement;
        }
    }
    
    /// <summary>
    /// Packet for weapon firing
    /// </summary>
    public class FireWeaponPacket : NetworkPacket
    {
        public string WeaponId { get; set; } = string.Empty;
        public Vector3 FirePosition { get; set; }
        public Vector3 FireDirection { get; set; }
        public string TargetId { get; set; } = string.Empty;
        public float Damage { get; set; }
        public DamageType DamageType { get; set; }
        public bool IsCritical { get; set; }
        
        public FireWeaponPacket()
        {
            Type = PacketType.FireWeapon;
        }
    }
    
    /// <summary>
    /// Packet for using abilities
    /// </summary>
    public class UseAbilityPacket : NetworkPacket
    {
        public string AbilityId { get; set; } = string.Empty;
        public Vector3 TargetPosition { get; set; }
        public string TargetId { get; set; } = string.Empty;
        public float SystemPowerCost { get; set; }
        
        public UseAbilityPacket()
        {
            Type = PacketType.UseAbility;
        }
    }
    
    /// <summary>
    /// Packet for chat messages
    /// </summary>
    public class ChatMessagePacket : NetworkPacket
    {
        public string Message { get; set; } = string.Empty;
        public ChatChannel Channel { get; set; }
        public string RecipientId { get; set; } = string.Empty; // For private messages
        
        public ChatMessagePacket()
        {
            Type = PacketType.ChatMessage;
        }
    }
    
    /// <summary>
    /// Packet for player status updates
    /// </summary>
    public class PlayerStatusPacket : NetworkPacket
    {
        public float Health { get; set; }
        public float SystemPower { get; set; }
        public bool IsAlive { get; set; }
        public bool IsInCombat { get; set; }
        
        public PlayerStatusPacket()
        {
            Type = PacketType.PlayerStatus;
        }
    }
    
    /// <summary>
    /// Packet for item interactions
    /// </summary>
    public class ItemInteractionPacket : NetworkPacket
    {
        public string ItemId { get; set; } = string.Empty;
        public ItemInteractionType InteractionType { get; set; }
        public Vector3 Position { get; set; }
        
        public ItemInteractionPacket()
        {
            Type = PacketType.ItemInteraction;
        }
    }
    
    public enum PacketType
    {
        PlayerMovement,
        FireWeapon,
        UseAbility,
        ChatMessage,
        PlayerStatus,
        ItemInteraction,
        ZoneTransfer,
        Heartbeat,
        Disconnect
    }
    
    public enum MovementState
    {
        Idle,
        Walking,
        Running,
        Crouching,
        Jumping,
        Falling
    }
    
    public enum ChatChannel
    {
        Local,
        Zone,
        Guild,
        Private,
        System,
        Trade
    }
    
    public enum ItemInteractionType
    {
        Pickup,
        Drop,
        Use,
        Equip,
        Unequip,
        Trade
    }
}
