using System;

namespace MMO.Shared.Models.Networking
{
    /// <summary>
    /// Contains information needed to connect to a game server
    /// </summary>
    public class ServerConnectionInfo
    {
        public string ZoneId { get; set; } = string.Empty;
        public string ZoneName { get; set; } = string.Empty;
        public string ServerAddress { get; set; } = string.Empty;
        public int ServerPort { get; set; } = 7777;
        public string ConnectionToken { get; set; } = string.Empty;
        public DateTime TokenExpiry { get; set; } = DateTime.UtcNow.AddMinutes(5);
        public int MaxPlayers { get; set; } = 100;
        public int CurrentPlayers { get; set; } = 0;
        public ServerStatus Status { get; set; } = ServerStatus.Online;
        public string Region { get; set; } = string.Empty;
        public DateTime LastHeartbeat { get; set; } = DateTime.UtcNow;
        
        /// <summary>
        /// Checks if the connection token is still valid
        /// </summary>
        public bool IsTokenValid => DateTime.UtcNow < TokenExpiry;
        
        /// <summary>
        /// Checks if the server has capacity for more players
        /// </summary>
        public bool HasCapacity => CurrentPlayers < MaxPlayers;
        
        /// <summary>
        /// Gets the server load as a percentage
        /// </summary>
        public float LoadPercentage => MaxPlayers > 0 ? (float)CurrentPlayers / MaxPlayers * 100f : 0f;
    }
    
    public enum ServerStatus
    {
        Online,
        Offline,
        Maintenance,
        Full,
        Starting,
        Stopping
    }
}
